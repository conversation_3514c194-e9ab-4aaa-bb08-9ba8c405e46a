#!/usr/bin/env python3
"""
串行版本：使用数值积分计算π值
数学原理：∫₀¹ 4/(1+x²) dx = π
使用矩形法则进行数值积分
"""

import time
import math

def f(x):
    """被积函数 f(x) = 4/(1+x²)"""
    return 4.0 / (1.0 + x * x)

def calculate_pi_serial(n):
    """
    串行计算π值
    参数:
        n: 积分区间的分割数量
    返回:
        计算得到的π值
    """
    h = 1.0 / n  # 步长
    sum_val = 0.0
    
    # 使用矩形法则计算积分
    for i in range(1, n + 1):
        x = h * (i - 0.5)  # 矩形中点
        sum_val += f(x)
    
    pi_approx = h * sum_val
    return pi_approx

def main():
    """主函数"""
    # 设置积分区间分割数量
    n = 10000000  # 1000万个区间
    
    print(f"计算π值 - 串行版本")
    print(f"积分区间数量: {n}")
    print(f"理论π值: {math.pi}")
    
    # 开始计时
    start_time = time.time()
    
    # 计算π值
    pi_calculated = calculate_pi_serial(n)
    
    # 结束计时
    end_time = time.time()
    
    # 计算误差
    error = abs(pi_calculated - math.pi)
    
    # 输出结果
    print(f"计算π值: {pi_calculated:.16f}")
    print(f"误差: {error:.16f}")
    print(f"计算时间: {end_time - start_time:.6f} 秒")

if __name__ == "__main__":
    main()
