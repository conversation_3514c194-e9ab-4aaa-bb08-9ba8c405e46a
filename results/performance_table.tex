\begin{tabular}{llrlll}
\toprule
Problem Size & Method & Threads & Time (s) & Speedup & Efficiency \\
\midrule
1M & Serial & 1 & 0.002755 & 1.00 & 1.00 \\
5M & Serial & 1 & 0.013817 & 1.00 & 1.00 \\
10M & Serial & 1 & 0.027901 & 1.00 & 1.00 \\
50M & Serial & 1 & 0.136585 & 1.00 & 1.00 \\
100M & Serial & 1 & 0.273649 & 1.00 & 1.00 \\
1M & OpenMP & 16 & 0.000171 & 16.07 & 1.004 \\
5M & OpenMP & 16 & 0.000706 & 19.56 & 1.223 \\
10M & OpenMP & 16 & 0.001855 & 15.04 & 0.940 \\
50M & OpenMP & 16 & 0.007247 & 18.85 & 1.178 \\
100M & OpenMP & 16 & 0.014821 & 18.46 & 1.154 \\
\bottomrule
\end{tabular}
