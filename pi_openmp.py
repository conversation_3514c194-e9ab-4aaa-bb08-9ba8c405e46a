#!/usr/bin/env python3
"""
多线程版本：使用数值积分计算π值（模拟OpenMP）
使用Python的concurrent.futures模拟OpenMP的并行计算
"""

import time
import math
import concurrent.futures
import threading
from functools import partial

def f(x):
    """被积函数 f(x) = 4/(1+x²)"""
    return 4.0 / (1.0 + x * x)

def calculate_partial_sum(start, end, h):
    """
    计算部分区间的积分和
    参数:
        start: 起始索引
        end: 结束索引
        h: 步长
    返回:
        部分积分和
    """
    local_sum = 0.0
    for i in range(start, end):
        x = h * (i - 0.5)  # 矩形中点
        local_sum += f(x)
    return local_sum

def calculate_pi_openmp(n, num_threads=None):
    """
    多线程并行计算π值
    参数:
        n: 积分区间的分割数量
        num_threads: 线程数量，默认为CPU核心数
    返回:
        计算得到的π值和使用的线程数
    """
    if num_threads is None:
        num_threads = threading.active_count()
        # 获取CPU核心数作为默认线程数
        import os
        num_threads = os.cpu_count() or 4
    
    h = 1.0 / n  # 步长
    
    # 计算每个线程处理的区间大小
    chunk_size = n // num_threads
    
    # 使用ThreadPoolExecutor进行并行计算
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = []
        
        # 为每个线程分配任务
        for i in range(num_threads):
            start = i * chunk_size + 1
            if i == num_threads - 1:
                # 最后一个线程处理剩余的所有区间
                end = n + 1
            else:
                end = (i + 1) * chunk_size + 1
            
            # 提交任务到线程池
            future = executor.submit(calculate_partial_sum, start, end, h)
            futures.append(future)
        
        # 收集所有线程的结果
        total_sum = 0.0
        for future in concurrent.futures.as_completed(futures):
            total_sum += future.result()
    
    pi_approx = h * total_sum
    return pi_approx, num_threads

def calculate_pi_openmp_simple(n, num_threads=4):
    """
    简化版多线程计算（更接近OpenMP的reduction模式）
    """
    import threading
    
    h = 1.0 / n
    total_sum = 0.0
    sum_lock = threading.Lock()
    
    def worker_thread(thread_id, num_threads):
        nonlocal total_sum
        local_sum = 0.0
        
        # 每个线程处理自己的部分（类似OpenMP的循环分配）
        for i in range(thread_id + 1, n + 1, num_threads):
            x = h * (i - 0.5)
            local_sum += f(x)
        
        # 线程安全地累加到总和
        with sum_lock:
            total_sum += local_sum
    
    # 创建并启动线程
    threads = []
    for i in range(num_threads):
        thread = threading.Thread(target=worker_thread, args=(i, num_threads))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    pi_approx = h * total_sum
    return pi_approx, num_threads

def main():
    """主函数"""
    # 设置积分区间分割数量
    n = 10000000  # 1000万个区间
    num_threads = 4  # 线程数量
    
    print(f"计算π值 - 多线程版本（模拟OpenMP）")
    print(f"线程数量: {num_threads}")
    print(f"积分区间数量: {n}")
    print(f"理论π值: {math.pi}")
    
    # 方法1：使用ThreadPoolExecutor
    print("\n方法1：使用ThreadPoolExecutor")
    start_time = time.time()
    pi_calculated1, actual_threads1 = calculate_pi_openmp(n, num_threads)
    end_time = time.time()
    
    error1 = abs(pi_calculated1 - math.pi)
    print(f"计算π值: {pi_calculated1:.16f}")
    print(f"误差: {error1:.16f}")
    print(f"实际线程数: {actual_threads1}")
    print(f"计算时间: {end_time - start_time:.6f} 秒")
    
    # 方法2：使用简化版多线程（更接近OpenMP）
    print("\n方法2：简化版多线程（更接近OpenMP）")
    start_time = time.time()
    pi_calculated2, actual_threads2 = calculate_pi_openmp_simple(n, num_threads)
    end_time = time.time()
    
    error2 = abs(pi_calculated2 - math.pi)
    print(f"计算π值: {pi_calculated2:.16f}")
    print(f"误差: {error2:.16f}")
    print(f"实际线程数: {actual_threads2}")
    print(f"计算时间: {end_time - start_time:.6f} 秒")

if __name__ == "__main__":
    main()
