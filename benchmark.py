#!/usr/bin/env python3
"""
性能基准测试脚本
比较不同版本的π计算程序的性能
"""

import subprocess
import time
import os
import sys

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd)
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return -1, "", str(e)

def extract_time_from_output(output, pattern="计算时间:"):
    """从输出中提取计算时间"""
    lines = output.split('\n')
    for line in lines:
        if pattern in line:
            try:
                # 提取时间数值
                time_str = line.split(':')[1].strip().split()[0]
                return float(time_str)
            except:
                pass
    return None

def benchmark_cpp():
    """基准测试C++版本"""
    print("=== C++版本性能测试 ===")
    
    # 编译所有程序
    print("编译程序...")
    run_command("make clean", ".")
    run_command("make all", ".")
    
    results = {}
    
    # 测试串行版本
    print("\n测试串行版本...")
    for i in range(3):
        ret, out, err = run_command("./pi_serial", ".")
        if ret == 0:
            exec_time = extract_time_from_output(out)
            if exec_time:
                results.setdefault('cpp_serial', []).append(exec_time)
                print(f"  运行 {i+1}: {exec_time:.6f} 秒")
    
    # 测试OpenMP版本
    print("\n测试OpenMP版本...")
    for i in range(3):
        ret, out, err = run_command("./pi_openmp", ".")
        if ret == 0:
            exec_time = extract_time_from_output(out)
            if exec_time:
                results.setdefault('cpp_openmp', []).append(exec_time)
                print(f"  运行 {i+1}: {exec_time:.6f} 秒")
    
    # 测试MPI版本
    print("\n测试MPI版本...")
    for i in range(3):
        ret, out, err = run_command("mpirun -np 4 ./pi_mpi", ".")
        if ret == 0:
            exec_time = extract_time_from_output(out)
            if exec_time:
                results.setdefault('cpp_mpi', []).append(exec_time)
                print(f"  运行 {i+1}: {exec_time:.6f} 秒")
    
    return results

def benchmark_python():
    """基准测试Python版本"""
    print("\n=== Python版本性能测试 ===")
    
    results = {}
    
    # 测试串行版本
    print("\n测试串行版本...")
    for i in range(3):
        ret, out, err = run_command("python3 pi_serial.py", ".")
        if ret == 0:
            exec_time = extract_time_from_output(out)
            if exec_time:
                results.setdefault('python_serial', []).append(exec_time)
                print(f"  运行 {i+1}: {exec_time:.6f} 秒")
    
    # 测试多线程版本
    print("\n测试多线程版本...")
    for i in range(3):
        ret, out, err = run_command("python3 pi_openmp.py", ".")
        if ret == 0:
            # 提取第一个方法的时间
            lines = out.split('\n')
            for j, line in enumerate(lines):
                if "方法1：使用ThreadPoolExecutor" in line:
                    # 查找下一个计算时间行
                    for k in range(j, min(j+10, len(lines))):
                        if "计算时间:" in lines[k]:
                            exec_time = extract_time_from_output(lines[k])
                            if exec_time:
                                results.setdefault('python_threading', []).append(exec_time)
                                print(f"  运行 {i+1}: {exec_time:.6f} 秒")
                            break
                    break
    
    # 测试MPI版本
    print("\n测试MPI版本...")
    for i in range(3):
        # 先尝试真正的MPI
        ret, out, err = run_command("mpirun -np 4 python3 pi_mpi.py", ".")
        if ret != 0:
            # 如果MPI失败，使用模拟模式
            ret, out, err = run_command("python3 pi_mpi.py", ".")
        
        if ret == 0:
            exec_time = extract_time_from_output(out)
            if exec_time:
                results.setdefault('python_mpi', []).append(exec_time)
                print(f"  运行 {i+1}: {exec_time:.6f} 秒")
    
    return results

def print_summary(cpp_results, python_results):
    """打印性能总结"""
    print("\n" + "="*60)
    print("性能总结")
    print("="*60)
    
    all_results = {**cpp_results, **python_results}
    
    print(f"{'版本':<20} {'平均时间(秒)':<15} {'最快时间(秒)':<15} {'加速比':<10}")
    print("-" * 60)
    
    # 计算基准时间（串行C++版本）
    baseline_time = None
    if 'cpp_serial' in all_results and all_results['cpp_serial']:
        baseline_time = sum(all_results['cpp_serial']) / len(all_results['cpp_serial'])
    
    for name, times in all_results.items():
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            
            # 计算加速比
            speedup = ""
            if baseline_time and baseline_time > 0:
                speedup = f"{baseline_time / avg_time:.2f}x"
            
            # 格式化名称
            display_name = {
                'cpp_serial': 'C++ 串行',
                'cpp_openmp': 'C++ OpenMP',
                'cpp_mpi': 'C++ MPI',
                'python_serial': 'Python 串行',
                'python_threading': 'Python 多线程',
                'python_mpi': 'Python MPI'
            }.get(name, name)
            
            print(f"{display_name:<20} {avg_time:<15.6f} {min_time:<15.6f} {speedup:<10}")

def main():
    """主函数"""
    print("π值计算程序性能基准测试")
    print("测试配置：1000万个积分区间")
    
    # 检查当前目录
    if not os.path.exists("pi_serial.cpp"):
        print("错误：请在包含源代码的目录中运行此脚本")
        sys.exit(1)
    
    # 运行C++基准测试
    cpp_results = benchmark_cpp()
    
    # 运行Python基准测试
    python_results = benchmark_python()
    
    # 打印总结
    print_summary(cpp_results, python_results)
    
    print("\n注意：")
    print("- 加速比是相对于C++串行版本计算的")
    print("- 每个版本运行3次取平均值")
    print("- MPI版本使用4个进程")
    print("- OpenMP版本使用系统默认线程数")

if __name__ == "__main__":
    main()
