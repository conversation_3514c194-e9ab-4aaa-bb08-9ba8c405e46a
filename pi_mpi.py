#!/usr/bin/env python3
"""
MPI并行版本：使用数值积分计算π值
需要安装mpi4py: pip install mpi4py
运行方式: mpirun -np 4 python pi_mpi.py
"""

try:
    from mpi4py import MPI
    MPI_AVAILABLE = True
except ImportError:
    MPI_AVAILABLE = False
    print("警告: mpi4py未安装，将使用模拟MPI模式")

import time
import math

def f(x):
    """被积函数 f(x) = 4/(1+x²)"""
    return 4.0 / (1.0 + x * x)

def calculate_pi_mpi(n):
    """
    MPI并行计算π值
    参数:
        n: 积分区间的分割数量
    返回:
        计算得到的π值
    """
    if MPI_AVAILABLE:
        comm = MPI.COMM_WORLD
        rank = comm.Get_rank()
        size = comm.Get_size()
    else:
        # 模拟单进程MPI
        rank = 0
        size = 1
        comm = None
    
    h = 1.0 / n  # 步长
    local_sum = 0.0
    
    # 每个进程计算自己负责的部分
    # 进程rank负责计算从rank开始，步长为size的所有点
    for i in range(rank + 1, n + 1, size):
        x = h * (i - 0.5)  # 矩形中点
        local_sum += f(x)
    
    if MPI_AVAILABLE:
        # 使用MPI_Reduce收集所有进程的结果
        total_sum = comm.reduce(local_sum, op=MPI.SUM, root=0)
        
        if rank == 0:
            pi_approx = h * total_sum
            return pi_approx, rank, size
        else:
            return None, rank, size
    else:
        # 模拟模式，直接返回结果
        pi_approx = h * local_sum
        return pi_approx, rank, size

def main():
    """主函数"""
    # 设置积分区间分割数量
    n = 10000000  # 1000万个区间
    
    if MPI_AVAILABLE:
        comm = MPI.COMM_WORLD
        rank = comm.Get_rank()
        size = comm.Get_size()
    else:
        rank = 0
        size = 1
    
    if rank == 0:
        print(f"计算π值 - MPI并行版本")
        print(f"进程数量: {size}")
        print(f"积分区间数量: {n}")
        print(f"理论π值: {math.pi}")
        
        # 开始计时
        start_time = time.time()
    
    # 计算π值
    result, my_rank, total_procs = calculate_pi_mpi(n)
    
    if rank == 0:
        # 结束计时
        end_time = time.time()
        
        if result is not None:
            # 计算误差
            error = abs(result - math.pi)
            
            # 输出结果
            print(f"计算π值: {result:.16f}")
            print(f"误差: {error:.16f}")
            print(f"计算时间: {end_time - start_time:.6f} 秒")
        else:
            print("计算失败")

if __name__ == "__main__":
    main()
