#include "dot_product.h"

/**
 * 串行点积计算实现
 * 
 * 该函数实现了两个向量的标准点积运算，采用最直接的串行算法。
 * 计算复杂度为O(n)，其中n为向量维度。
 * 
 * @param a 第一个输入向量
 * @param b 第二个输入向量
 * @return 两向量的点积结果
 */
double serial_dot_product(const Vector& a, const Vector& b) {
    if (a.size() != b.size()) {
        throw std::invalid_argument("向量维度不匹配");
    }
    
    double sum = 0.0;
    const size_t n = a.size();
    
    // 串行计算点积
    for (size_t i = 0; i < n; ++i) {
        sum += a[i] * b[i];
    }
    
    return sum;
}

/**
 * 生成指定大小的随机向量
 * 
 * @param size 向量大小
 * @param min_val 最小值
 * @param max_val 最大值
 * @return 生成的随机向量
 */
Vector generate_random_vector(int size, double min_val, double max_val) {
    Vector vec(size);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<double> dis(min_val, max_val);
    
    for (int i = 0; i < size; ++i) {
        vec[i] = dis(gen);
    }
    
    return vec;
}

/**
 * 打印向量信息
 */
void print_vector_info(const Vector& vec, const std::string& name) {
    std::cout << name << " 向量信息:" << std::endl;
    std::cout << "  大小: " << vec.size() << std::endl;
    
    if (vec.size() <= 10) {
        std::cout << "  元素: [";
        for (size_t i = 0; i < vec.size(); ++i) {
            std::cout << std::fixed << std::setprecision(3) << vec[i];
            if (i < vec.size() - 1) std::cout << ", ";
        }
        std::cout << "]" << std::endl;
    } else {
        std::cout << "  前5个元素: [";
        for (int i = 0; i < 5; ++i) {
            std::cout << std::fixed << std::setprecision(3) << vec[i];
            if (i < 4) std::cout << ", ";
        }
        std::cout << ", ...]" << std::endl;
    }
}

/**
 * 测量函数执行时间
 */
double measure_execution_time(std::function<double()> func) {
    auto start = std::chrono::high_resolution_clock::now();
    double result = func();
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    return duration.count() / 1000.0; // 返回毫秒
}
