#!/usr/bin/env python3
"""
生成主要的性能分析图表 - 专为报告设计
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

# 设置学术论文风格
plt.rcParams.update({
    'font.family': 'serif',
    'font.serif': ['Times New Roman'],
    'font.size': 12,
    'axes.titlesize': 14,
    'axes.labelsize': 13,
    'xtick.labelsize': 11,
    'ytick.labelsize': 11,
    'legend.fontsize': 11,
    'figure.titlesize': 16,
    'axes.unicode_minus': False,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

# 专业配色方案
colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

def create_main_performance_figure():
    """创建主要性能分析图表"""
    # 加载数据
    df = pd.read_csv('results/performance_data.csv')
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 9))
    
    openmp_data = df[df['Method'] == 'OpenMP']
    serial_data = df[df['Method'] == 'Serial']
    problem_sizes = sorted(openmp_data['Problem_Size'].unique())
    
    # 1. 加速比分析 (左上)
    markers = ['o', 's', '^', 'D', 'v']
    for i, size in enumerate(problem_sizes):
        size_data = openmp_data[openmp_data['Problem_Size'] == size]
        label = f'{size/1e6:.0f}M intervals'
        ax1.plot(size_data['Threads'], size_data['Speedup'], 
                marker=markers[i], linewidth=2.5, markersize=7,
                label=label, color=colors[i], alpha=0.8)
    
    # 理想加速比线
    max_threads = openmp_data['Threads'].max()
    ax1.plot(range(1, max_threads + 1), range(1, max_threads + 1), 
             '--', color='gray', linewidth=2, alpha=0.7, label='Ideal Speedup')
    
    ax1.set_xlabel('Number of Threads')
    ax1.set_ylabel('Speedup')
    ax1.set_title('(a) Speedup vs Thread Count', fontweight='bold')
    ax1.legend(fontsize=9, frameon=True, fancybox=True)
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(0.5, max_threads + 0.5)
    ax1.set_ylim(0, 22)
    
    # 2. 并行效率 (右上)
    for i, size in enumerate(problem_sizes):
        size_data = openmp_data[openmp_data['Problem_Size'] == size]
        efficiency = size_data['Speedup'] / size_data['Threads']
        label = f'{size/1e6:.0f}M intervals'
        ax2.plot(size_data['Threads'], efficiency, 
                marker=markers[i], linewidth=2.5, markersize=7,
                label=label, color=colors[i], alpha=0.8)
    
    ax2.axhline(y=1.0, color='gray', linestyle='--', linewidth=2, alpha=0.7)
    ax2.set_xlabel('Number of Threads')
    ax2.set_ylabel('Parallel Efficiency')
    ax2.set_title('(b) Parallel Efficiency', fontweight='bold')
    ax2.legend(fontsize=9, frameon=True, fancybox=True)
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(0.5, max_threads + 0.5)
    ax2.set_ylim(0, 1.4)
    
    # 3. 执行时间对比 (左下)
    serial_times = [serial_data[serial_data['Problem_Size'] == size]['Time_Avg'].iloc[0] 
                   for size in problem_sizes]
    
    ax3.loglog(problem_sizes, serial_times, 'o-', linewidth=3, markersize=8, 
               label='Serial', color=colors[0], alpha=0.8)
    
    # 16线程OpenMP时间
    openmp_16_data = openmp_data[openmp_data['Threads'] == 16]
    openmp_times = []
    for size in problem_sizes:
        size_data = openmp_16_data[openmp_16_data['Problem_Size'] == size]
        if not size_data.empty:
            openmp_times.append(size_data['Time_Avg'].iloc[0])
        else:
            openmp_times.append(np.nan)
    
    ax3.loglog(problem_sizes, openmp_times, 's-', linewidth=3, markersize=8, 
               label='OpenMP (16 threads)', color=colors[1], alpha=0.8)
    
    ax3.set_xlabel('Problem Size (intervals)')
    ax3.set_ylabel('Execution Time (seconds)')
    ax3.set_title('(c) Performance Scaling', fontweight='bold')
    ax3.legend(fontsize=10, frameon=True, fancybox=True)
    ax3.grid(True, alpha=0.3, which='both')
    
    # 格式化x轴标签
    ax3.set_xticks(problem_sizes)
    ax3.set_xticklabels([f'{size/1e6:.0f}M' for size in problem_sizes])
    
    # 4. 强可扩展性分析 (右下)
    # 选择最大问题规模进行强可扩展性分析
    largest_size = max(problem_sizes)
    largest_data = openmp_data[openmp_data['Problem_Size'] == largest_size]
    
    threads = largest_data['Threads'].values
    speedup = largest_data['Speedup'].values
    efficiency = speedup / threads
    
    ax4_twin = ax4.twinx()
    
    # 加速比
    line1 = ax4.plot(threads, speedup, 'o-', linewidth=3, markersize=8, 
                     color=colors[0], label='Speedup', alpha=0.8)
    ax4.plot(threads, threads, '--', color='gray', linewidth=2, alpha=0.7, label='Ideal')
    
    # 效率
    line2 = ax4_twin.plot(threads, efficiency, 's-', linewidth=3, markersize=8, 
                          color=colors[1], label='Efficiency', alpha=0.8)
    
    ax4.set_xlabel('Number of Threads')
    ax4.set_ylabel('Speedup', color=colors[0])
    ax4_twin.set_ylabel('Parallel Efficiency', color=colors[1])
    ax4.set_title(f'(d) Strong Scalability ({largest_size/1e6:.0f}M intervals)', fontweight='bold')
    
    # 合并图例
    lines = line1 + line2 + [plt.Line2D([0], [0], color='gray', linestyle='--', label='Ideal')]
    labels = [l.get_label() for l in lines]
    ax4.legend(lines, labels, fontsize=10, frameon=True, fancybox=True)
    
    ax4.grid(True, alpha=0.3)
    ax4.set_xlim(0.5, max_threads + 0.5)
    ax4.set_ylim(0, 22)
    ax4_twin.set_ylim(0, 1.4)
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    plt.suptitle('Parallel Performance Analysis of π Calculation', 
                fontsize=16, fontweight='bold')
    
    # 保存图表
    plt.savefig('results/main_performance_figure.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.savefig('results/main_performance_figure.pdf', bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("✓ 主要性能分析图表已生成 (PNG和PDF格式)")

def create_summary_table():
    """创建性能摘要表格"""
    df = pd.read_csv('results/performance_data.csv')
    
    # 创建摘要表格
    summary_data = []
    
    # 串行基准
    serial_data = df[df['Method'] == 'Serial']
    for _, row in serial_data.iterrows():
        summary_data.append({
            'Problem Size': f"{row['Problem_Size']/1e6:.0f}M",
            'Method': 'Serial',
            'Threads': 1,
            'Time (s)': f"{row['Time_Avg']:.6f}",
            'Speedup': '1.00',
            'Efficiency': '1.00'
        })
    
    # OpenMP 16线程结果
    openmp_16 = df[(df['Method'] == 'OpenMP') & (df['Threads'] == 16)]
    for _, row in openmp_16.iterrows():
        efficiency = row['Speedup'] / 16
        summary_data.append({
            'Problem Size': f"{row['Problem_Size']/1e6:.0f}M",
            'Method': 'OpenMP',
            'Threads': 16,
            'Time (s)': f"{row['Time_Avg']:.6f}",
            'Speedup': f"{row['Speedup']:.2f}",
            'Efficiency': f"{efficiency:.3f}"
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv('results/performance_summary_table.csv', index=False)
    
    # 生成LaTeX表格
    latex_table = summary_df.to_latex(index=False, float_format="%.3f")
    with open('results/performance_table.tex', 'w') as f:
        f.write(latex_table)
    
    print("✓ 性能摘要表格已生成 (CSV和LaTeX格式)")

if __name__ == "__main__":
    print("生成主要性能分析图表...")
    create_main_performance_figure()
    create_summary_table()
    print("完成！")
