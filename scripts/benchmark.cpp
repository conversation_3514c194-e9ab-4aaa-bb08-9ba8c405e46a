/*
 * 性能基准测试程序
 * 自动测试不同版本的π计算程序并收集性能数据
 * 
 * 编译: g++ -O2 -o benchmark benchmark.cpp -fopenmp
 * 运行: ./benchmark
 */

#include <iostream>
#include <fstream>
#include <vector>
#include <chrono>
#include <cmath>
#include <iomanip>
#include <omp.h>

// 被积函数 f(x) = 4/(1+x²)
double f(double x) {
    return 4.0 / (1.0 + x * x);
}

// 串行计算π值
double calculate_pi_serial(int n, double& time_taken) {
    auto start = std::chrono::high_resolution_clock::now();
    
    double h = 1.0 / (double)n;
    double sum = 0.0;
    
    for (int i = 1; i <= n; i++) {
        double x = h * ((double)i - 0.5);
        sum += f(x);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    time_taken = std::chrono::duration<double>(end - start).count();
    
    return h * sum;
}

// OpenMP并行计算π值
double calculate_pi_openmp(int n, int num_threads, double& time_taken) {
    omp_set_num_threads(num_threads);
    
    auto start = std::chrono::high_resolution_clock::now();
    
    double h = 1.0 / (double)n;
    double sum = 0.0;
    
    #pragma omp parallel for reduction(+:sum)
    for (int i = 0; i < n; i++) {
        double x = h * ((double)i + 0.5);
        sum += f(x);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    time_taken = std::chrono::duration<double>(end - start).count();
    
    return h * sum;
}

// 运行基准测试
void run_benchmark() {
    const double PI_EXACT = 3.1415926535897932384626433;
    std::vector<int> problem_sizes = {1000000, 5000000, 10000000, 50000000, 100000000};
    std::vector<int> thread_counts = {1, 2, 4, 8, 16};
    const int num_runs = 5;
    
    std::ofstream csv_file("results/performance_data.csv");
    csv_file << "Problem_Size,Method,Threads,Time_Avg,Time_Min,Time_Max,Error,Speedup\n";
    
    std::cout << std::fixed << std::setprecision(6);
    std::cout << "开始性能基准测试...\n\n";
    
    for (int n : problem_sizes) {
        std::cout << "测试问题规模: " << n << " 个积分区间\n";
        
        // 串行基准测试
        std::vector<double> serial_times;
        double serial_pi = 0.0;
        
        for (int run = 0; run < num_runs; run++) {
            double time_taken;
            serial_pi = calculate_pi_serial(n, time_taken);
            serial_times.push_back(time_taken);
        }
        
        double serial_avg = 0.0, serial_min = serial_times[0], serial_max = serial_times[0];
        for (double t : serial_times) {
            serial_avg += t;
            serial_min = std::min(serial_min, t);
            serial_max = std::max(serial_max, t);
        }
        serial_avg /= num_runs;
        
        double serial_error = std::abs(serial_pi - PI_EXACT);
        
        std::cout << "  串行版本: " << serial_avg << "s, 误差: " << serial_error << "\n";
        
        csv_file << n << ",Serial,1," << serial_avg << "," << serial_min << "," 
                 << serial_max << "," << serial_error << ",1.0\n";
        
        // OpenMP并行测试
        for (int threads : thread_counts) {
            if (threads == 1) continue; // 跳过单线程OpenMP
            
            std::vector<double> openmp_times;
            double openmp_pi = 0.0;
            
            for (int run = 0; run < num_runs; run++) {
                double time_taken;
                openmp_pi = calculate_pi_openmp(n, threads, time_taken);
                openmp_times.push_back(time_taken);
            }
            
            double openmp_avg = 0.0, openmp_min = openmp_times[0], openmp_max = openmp_times[0];
            for (double t : openmp_times) {
                openmp_avg += t;
                openmp_min = std::min(openmp_min, t);
                openmp_max = std::max(openmp_max, t);
            }
            openmp_avg /= num_runs;
            
            double openmp_error = std::abs(openmp_pi - PI_EXACT);
            double speedup = serial_avg / openmp_avg;
            
            std::cout << "  OpenMP(" << threads << "线程): " << openmp_avg 
                     << "s, 加速比: " << speedup << "x\n";
            
            csv_file << n << ",OpenMP," << threads << "," << openmp_avg << "," 
                     << openmp_min << "," << openmp_max << "," << openmp_error 
                     << "," << speedup << "\n";
        }
        
        std::cout << "\n";
    }
    
    csv_file.close();
    std::cout << "基准测试完成，结果已保存到 results/performance_data.csv\n";
}

int main() {
    std::cout << "π值计算性能基准测试程序\n";
    std::cout << "=============================\n\n";
    
    // 显示系统信息
    std::cout << "系统信息:\n";
    std::cout << "  最大OpenMP线程数: " << omp_get_max_threads() << "\n";
    std::cout << "  处理器核心数: " << omp_get_num_procs() << "\n\n";
    
    run_benchmark();
    
    return 0;
}
