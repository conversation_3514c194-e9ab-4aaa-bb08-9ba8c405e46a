/*
 * 性能基准测试程序
 * 自动测试不同版本的π计算程序并收集性能数据
 * 
 * 编译: g++ -O2 -o benchmark benchmark.cpp -fopenmp
 * 运行: ./benchmark
 */

#include <iostream>
#include <fstream>
#include <vector>
#include <chrono>
#include <cmath>
#include <iomanip>
#include <omp.h>
#include <cstdlib>
#include <sstream>
#include <string>

// 被积函数 f(x) = 4/(1+x²)
double f(double x) {
    return 4.0 / (1.0 + x * x);
}

// 串行计算π值
double calculate_pi_serial(int n, double& time_taken) {
    auto start = std::chrono::high_resolution_clock::now();
    
    double h = 1.0 / (double)n;
    double sum = 0.0;
    
    for (int i = 1; i <= n; i++) {
        double x = h * ((double)i - 0.5);
        sum += f(x);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    time_taken = std::chrono::duration<double>(end - start).count();
    
    return h * sum;
}

// OpenMP并行计算π值
double calculate_pi_openmp(int n, int num_threads, double& time_taken) {
    omp_set_num_threads(num_threads);
    
    auto start = std::chrono::high_resolution_clock::now();
    
    double h = 1.0 / (double)n;
    double sum = 0.0;
    
    #pragma omp parallel for reduction(+:sum)
    for (int i = 0; i < n; i++) {
        double x = h * ((double)i + 0.5);
        sum += f(x);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    time_taken = std::chrono::duration<double>(end - start).count();
    
    return h * sum;
}

// 检查MPI是否可用
bool is_mpi_available() {
    int result = system("which mpirun > /dev/null 2>&1");
    return (result == 0);
}

// MPI并行计算π值（通过调用外部程序）
std::pair<double, double> calculate_pi_mpi(int n, int num_procs) {
    if (!is_mpi_available()) {
        return {-1.0, -1.0};
    }

    auto start = std::chrono::high_resolution_clock::now();

    // 构建MPI命令，将积分区间数作为参数传递
    std::stringstream cmd;
    cmd << "echo " << n << " | mpirun -np " << num_procs << " ./pi_mpi 2>/dev/null";

    // 执行MPI程序并捕获输出
    FILE* pipe = popen(cmd.str().c_str(), "r");
    if (!pipe) {
        return {-1.0, -1.0};
    }

    char buffer[1024];
    std::string result;
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        result += buffer;
    }
    int exit_code = pclose(pipe);

    auto end = std::chrono::high_resolution_clock::now();
    double time_taken = std::chrono::duration<double>(end - start).count();

    if (exit_code != 0) {
        return {-1.0, -1.0};
    }

    // 解析π值
    size_t pos = result.find("计算π值:");
    if (pos != std::string::npos) {
        std::string line = result.substr(pos);
        size_t end_pos = line.find('\n');
        if (end_pos != std::string::npos) {
            line = line.substr(0, end_pos);
        }

        // 提取数值部分
        size_t colon_pos = line.find(':');
        if (colon_pos != std::string::npos) {
            std::string pi_str = line.substr(colon_pos + 1);
            // 移除空格
            pi_str.erase(0, pi_str.find_first_not_of(" \t"));
            pi_str.erase(pi_str.find_last_not_of(" \t") + 1);

            try {
                double pi_value = std::stod(pi_str);
                return {pi_value, time_taken};
            } catch (...) {
                return {-1.0, -1.0};
            }
        }
    }

    return {-1.0, -1.0};
}

// 运行基准测试
void run_benchmark() {
    const double PI_EXACT = 3.1415926535897932384626433;
    std::vector<int> problem_sizes = {1000000, 5000000, 10000000, 20000000};
    std::vector<int> thread_counts = {1, 2, 4, 8, 16};
    const int num_runs = 3;
    
    std::ofstream csv_file("results/performance_data.csv");
    csv_file << "Problem_Size,Method,Processes_Threads,Time_Avg,Time_Min,Time_Max,Error,Speedup\n";

    bool mpi_available = is_mpi_available();
    if (mpi_available) {
        std::cout << "MPI环境检测: 可用\n";
    } else {
        std::cout << "MPI环境检测: 不可用，将跳过MPI测试\n";
    }
    
    std::cout << std::fixed << std::setprecision(6);
    std::cout << "开始性能基准测试...\n\n";
    
    for (int n : problem_sizes) {
        std::cout << "测试问题规模: " << n << " 个积分区间\n";
        
        // 串行基准测试
        std::vector<double> serial_times;
        double serial_pi = 0.0;
        
        for (int run = 0; run < num_runs; run++) {
            double time_taken;
            serial_pi = calculate_pi_serial(n, time_taken);
            serial_times.push_back(time_taken);
        }
        
        double serial_avg = 0.0, serial_min = serial_times[0], serial_max = serial_times[0];
        for (double t : serial_times) {
            serial_avg += t;
            serial_min = std::min(serial_min, t);
            serial_max = std::max(serial_max, t);
        }
        serial_avg /= num_runs;
        
        double serial_error = std::abs(serial_pi - PI_EXACT);
        
        std::cout << "  串行版本: " << serial_avg << "s, 误差: " << serial_error << "\n";
        
        csv_file << n << ",Serial,1," << serial_avg << "," << serial_min << ","
                 << serial_max << "," << serial_error << ",1.0\n";
        
        // OpenMP并行测试
        for (int threads : thread_counts) {
            if (threads == 1) continue; // 跳过单线程OpenMP
            
            std::vector<double> openmp_times;
            double openmp_pi = 0.0;
            
            for (int run = 0; run < num_runs; run++) {
                double time_taken;
                openmp_pi = calculate_pi_openmp(n, threads, time_taken);
                openmp_times.push_back(time_taken);
            }
            
            double openmp_avg = 0.0, openmp_min = openmp_times[0], openmp_max = openmp_times[0];
            for (double t : openmp_times) {
                openmp_avg += t;
                openmp_min = std::min(openmp_min, t);
                openmp_max = std::max(openmp_max, t);
            }
            openmp_avg /= num_runs;
            
            double openmp_error = std::abs(openmp_pi - PI_EXACT);
            double speedup = serial_avg / openmp_avg;
            
            std::cout << "  OpenMP(" << threads << "线程): " << openmp_avg 
                     << "s, 加速比: " << speedup << "x\n";
            
            csv_file << n << ",OpenMP," << threads << "," << openmp_avg << ","
                     << openmp_min << "," << openmp_max << "," << openmp_error
                     << "," << speedup << "\n";
        }

        // MPI并行测试
        if (mpi_available) {
            std::vector<int> process_counts = {2, 4, 8};
            for (int procs : process_counts) {
                std::vector<double> mpi_times;
                std::vector<double> mpi_pi_values;

                std::cout << "  测试MPI(" << procs << "进程)...";

                for (int run = 0; run < num_runs; run++) {
                    auto result = calculate_pi_mpi(n, procs);
                    if (result.first > 0 && result.second > 0) {
                        mpi_pi_values.push_back(result.first);
                        mpi_times.push_back(result.second);
                    }
                }

                if (!mpi_times.empty()) {
                    double mpi_avg = 0.0, mpi_min = mpi_times[0], mpi_max = mpi_times[0];
                    for (double t : mpi_times) {
                        mpi_avg += t;
                        mpi_min = std::min(mpi_min, t);
                        mpi_max = std::max(mpi_max, t);
                    }
                    mpi_avg /= mpi_times.size();

                    double mpi_pi_avg = 0.0;
                    for (double pi : mpi_pi_values) {
                        mpi_pi_avg += pi;
                    }
                    mpi_pi_avg /= mpi_pi_values.size();

                    double mpi_error = std::abs(mpi_pi_avg - PI_EXACT);
                    double mpi_speedup = serial_avg / mpi_avg;

                    std::cout << " " << mpi_avg << "s, 加速比: " << mpi_speedup << "x\n";

                    csv_file << n << ",MPI," << procs << "," << mpi_avg << ","
                             << mpi_min << "," << mpi_max << "," << mpi_error
                             << "," << mpi_speedup << "\n";
                } else {
                    std::cout << " 失败\n";
                }
            }
        }

        std::cout << "\n";
    }
    
    csv_file.close();
    std::cout << "基准测试完成，结果已保存到 results/performance_data.csv\n";
}

int main() {
    std::cout << "π值计算性能基准测试程序\n";
    std::cout << "=============================\n\n";
    
    // 显示系统信息
    std::cout << "系统信息:\n";
    std::cout << "  最大OpenMP线程数: " << omp_get_max_threads() << "\n";
    std::cout << "  处理器核心数: " << omp_get_num_procs() << "\n\n";
    
    run_benchmark();
    
    return 0;
}
