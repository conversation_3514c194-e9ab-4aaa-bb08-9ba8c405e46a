#!/bin/bash

# 点积并行计算项目编译脚本
# 支持串行、OpenMP和MPI三种版本的编译

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查编译器
check_compilers() {
    print_info "检查编译器可用性..."
    
    if ! command -v g++ &> /dev/null; then
        print_error "g++ 编译器未找到，请安装 g++"
        exit 1
    fi
    
    if ! command -v mpic++ &> /dev/null; then
        print_warning "mpic++ 编译器未找到，将跳过MPI版本编译"
        MPI_AVAILABLE=false
    else
        MPI_AVAILABLE=true
    fi
    
    print_success "编译器检查完成"
}

# 创建构建目录
setup_directories() {
    print_info "设置构建目录..."
    
    mkdir -p build
    mkdir -p bin
    mkdir -p results
    
    print_success "目录设置完成"
}

# 编译串行和OpenMP版本
build_openmp_version() {
    print_info "编译串行和OpenMP版本..."
    
    # 编译选项
    CXX_FLAGS="-std=c++17 -O3 -Wall -Wextra"
    OPENMP_FLAGS="-fopenmp"
    
    # 源文件
    SOURCES="src/serial_dot_product.cpp src/openmp_dot_product.cpp src/benchmark.cpp src/main.cpp"
    
    # 编译命令
    COMPILE_CMD="g++ ${CXX_FLAGS} ${OPENMP_FLAGS} ${SOURCES} -o bin/dot_product_openmp"
    
    print_info "执行编译命令: ${COMPILE_CMD}"
    
    if eval ${COMPILE_CMD}; then
        print_success "OpenMP版本编译成功: bin/dot_product_openmp"
    else
        print_error "OpenMP版本编译失败"
        exit 1
    fi
}

# 编译MPI版本
build_mpi_version() {
    if [ "$MPI_AVAILABLE" = false ]; then
        print_warning "跳过MPI版本编译（mpic++不可用）"
        return
    fi
    
    print_info "编译MPI版本..."
    
    # 编译选项
    CXX_FLAGS="-std=c++17 -O3 -Wall -Wextra -DUSE_MPI"
    
    # 源文件
    SOURCES="src/serial_dot_product.cpp src/mpi_dot_product.cpp src/mpi_main.cpp"
    
    # 编译命令
    COMPILE_CMD="mpic++ ${CXX_FLAGS} ${SOURCES} -o bin/dot_product_mpi"
    
    print_info "执行编译命令: ${COMPILE_CMD}"
    
    if eval ${COMPILE_CMD}; then
        print_success "MPI版本编译成功: bin/dot_product_mpi"
    else
        print_error "MPI版本编译失败"
        exit 1
    fi
}

# 编译性能测试工具
build_benchmark_tool() {
    print_info "编译性能测试工具..."
    
    # 创建独立的性能测试程序
    cat > src/benchmark_main.cpp << 'EOF'
#include "dot_product.h"

int main() {
    std::cout << "=== 点积并行计算性能测试工具 ===" << std::endl;
    
    // 测试配置
    std::vector<int> sizes = {1000, 10000, 100000, 1000000, 10000000};
    std::vector<int> threads = {1, 2, 4, 8, 16};
    
    // 运行综合测试
    run_comprehensive_benchmark(sizes, threads);
    
    std::cout << "性能测试完成，结果已保存到 results/ 目录" << std::endl;
    
    return 0;
}
EOF
    
    # 编译性能测试工具
    CXX_FLAGS="-std=c++17 -O3 -Wall -Wextra"
    OPENMP_FLAGS="-fopenmp"
    SOURCES="src/serial_dot_product.cpp src/openmp_dot_product.cpp src/benchmark.cpp src/benchmark_main.cpp"
    
    COMPILE_CMD="g++ ${CXX_FLAGS} ${OPENMP_FLAGS} ${SOURCES} -o bin/benchmark"
    
    if eval ${COMPILE_CMD}; then
        print_success "性能测试工具编译成功: bin/benchmark"
    else
        print_error "性能测试工具编译失败"
        exit 1
    fi
}

# 生成运行脚本
generate_run_scripts() {
    print_info "生成运行脚本..."
    
    # OpenMP运行脚本
    cat > scripts/run_openmp.sh << 'EOF'
#!/bin/bash
echo "运行OpenMP版本点积计算..."
echo "可用CPU核心数: $(nproc)"
echo "================================"
./bin/dot_product_openmp
EOF
    
    # MPI运行脚本
    if [ "$MPI_AVAILABLE" = true ]; then
        cat > scripts/run_mpi.sh << 'EOF'
#!/bin/bash
PROCESSES=${1:-4}
echo "运行MPI版本点积计算..."
echo "使用进程数: $PROCESSES"
echo "================================"
mpirun -np $PROCESSES ./bin/dot_product_mpi
EOF
    fi
    
    # 性能测试脚本
    cat > scripts/run_benchmark.sh << 'EOF'
#!/bin/bash
echo "运行性能测试..."
echo "这可能需要几分钟时间..."
echo "================================"
./bin/benchmark
echo "测试完成，查看 results/ 目录获取详细结果"
EOF
    
    # 设置执行权限
    chmod +x scripts/run_*.sh
    
    print_success "运行脚本生成完成"
}

# 显示构建信息
show_build_info() {
    print_info "构建信息总结:"
    echo "================================"
    echo "编译器版本:"
    g++ --version | head -1
    
    if [ "$MPI_AVAILABLE" = true ]; then
        echo "MPI版本:"
        mpic++ --version | head -1
    fi
    
    echo ""
    echo "生成的可执行文件:"
    ls -la bin/
    
    echo ""
    echo "运行脚本:"
    ls -la scripts/run_*.sh
    
    echo ""
    print_success "构建完成！"
    echo ""
    echo "使用方法:"
    echo "  OpenMP版本: ./scripts/run_openmp.sh"
    if [ "$MPI_AVAILABLE" = true ]; then
        echo "  MPI版本:    ./scripts/run_mpi.sh [进程数]"
    fi
    echo "  性能测试:   ./scripts/run_benchmark.sh"
}

# 主函数
main() {
    echo "========================================"
    echo "    点积并行计算项目构建脚本"
    echo "========================================"
    
    check_compilers
    setup_directories
    build_openmp_version
    build_mpi_version
    build_benchmark_tool
    generate_run_scripts
    show_build_info
}

# 执行主函数
main "$@"
