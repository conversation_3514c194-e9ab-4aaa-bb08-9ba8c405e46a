#!/usr/bin/env python3
"""
数据可视化脚本
生成性能分析图表
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from pathlib import Path

# 设置高质量图表样式
plt.rcParams.update({
    'font.family': 'serif',
    'font.serif': ['Times New Roman', 'DejaVu Serif'],
    'font.size': 11,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.titlesize': 16,
    'axes.unicode_minus': False,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

# 设置专业配色方案
colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83', '#1B998B']
sns.set_palette(colors)

def load_data():
    """加载性能数据"""
    data_file = Path("results/performance_data.csv")
    if not data_file.exists():
        print("错误: 未找到性能数据文件，请先运行基准测试")
        return None

    df = pd.read_csv(data_file)
    # 重命名列以保持一致性
    if 'Processes_Threads' in df.columns:
        df = df.rename(columns={'Processes_Threads': 'Threads'})
    return df

def plot_speedup_analysis(df):
    """绘制加速比分析图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # 过滤数据
    openmp_data = df[df['Method'] == 'OpenMP']
    mpi_data = df[df['Method'] == 'MPI']

    # 加速比随线程/进程数变化
    problem_sizes = sorted(df['Problem_Size'].unique())
    markers = ['o', 's', '^', 'D']

    # OpenMP数据
    for i, size in enumerate(problem_sizes):
        size_data = openmp_data[openmp_data['Problem_Size'] == size]
        if not size_data.empty:
            label = f'OpenMP {size/1e6:.0f}M'
            ax1.plot(size_data['Threads'], size_data['Speedup'],
                    marker=markers[i % len(markers)], linewidth=2.5, markersize=6,
                    label=label, alpha=0.8, color=colors[i])

    # MPI数据
    for i, size in enumerate(problem_sizes):
        size_data = mpi_data[mpi_data['Problem_Size'] == size]
        if not size_data.empty:
            label = f'MPI {size/1e6:.0f}M'
            ax1.plot(size_data['Threads'], size_data['Speedup'],
                    marker=markers[i % len(markers)], linewidth=2.5, markersize=6,
                    label=label, alpha=0.8, color=colors[i], linestyle='--')

    # 理想加速比线
    max_threads = max(df['Threads'].max(), 16)
    ideal_threads = range(1, max_threads + 1)
    ax1.plot(ideal_threads, ideal_threads, '--', color='#666666',
             linewidth=2, alpha=0.7, label='Ideal Speedup')

    ax1.set_xlabel('Number of Threads/Processes')
    ax1.set_ylabel('Speedup')
    ax1.set_title('Speedup Comparison: OpenMP vs MPI', fontweight='bold', pad=15)
    ax1.legend(frameon=True, fancybox=True, shadow=True, ncol=2, fontsize=9)
    ax1.set_xlim(0.5, max_threads + 0.5)
    ax1.set_ylim(0, max(df['Speedup'].max() * 1.1, 20))
    ax1.grid(True, alpha=0.3)

    # 方法对比（选择最大问题规模）
    largest_size = max(problem_sizes)
    serial_data = df[df['Method'] == 'Serial']
    largest_serial = serial_data[serial_data['Problem_Size'] == largest_size]

    if not largest_serial.empty:
        serial_time = largest_serial['Time_Avg'].iloc[0]

        # OpenMP时间
        openmp_largest = openmp_data[openmp_data['Problem_Size'] == largest_size]
        if not openmp_largest.empty:
            ax2.plot(openmp_largest['Threads'], openmp_largest['Time_Avg'],
                    'o-', linewidth=3, markersize=8, label='OpenMP',
                    color=colors[0], alpha=0.8)

        # MPI时间
        mpi_largest = mpi_data[mpi_data['Problem_Size'] == largest_size]
        if not mpi_largest.empty:
            ax2.plot(mpi_largest['Threads'], mpi_largest['Time_Avg'],
                    's--', linewidth=3, markersize=8, label='MPI',
                    color=colors[1], alpha=0.8)

        # 串行基准线
        ax2.axhline(y=serial_time, color='#666666', linestyle='-',
                   linewidth=2, alpha=0.7, label=f'Serial ({serial_time:.3f}s)')

    ax2.set_xlabel('Number of Threads/Processes')
    ax2.set_ylabel('Execution Time (seconds)')
    ax2.set_title(f'Performance Comparison ({largest_size/1e6:.0f}M intervals)',
                  fontweight='bold', pad=15)
    ax2.legend(frameon=True, fancybox=True, shadow=True)
    ax2.set_yscale('log')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('results/speedup_analysis.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def plot_performance_comparison(df):
    """绘制性能对比图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # 执行时间对比
    problem_sizes = sorted(df['Problem_Size'].unique())

    # 串行时间
    serial_data = df[df['Method'] == 'Serial']
    serial_times = [serial_data[serial_data['Problem_Size'] == size]['Time_Avg'].iloc[0]
                   for size in problem_sizes]

    ax1.loglog(problem_sizes, serial_times, 'o-', linewidth=3, markersize=8,
               label='Serial', color=colors[0], alpha=0.8)

    # OpenMP最佳配置（选择每个问题规模的最佳线程数）
    openmp_data = df[df['Method'] == 'OpenMP']
    openmp_best_times = []
    for size in problem_sizes:
        size_data = openmp_data[openmp_data['Problem_Size'] == size]
        if not size_data.empty:
            best_time = size_data['Time_Avg'].min()
            openmp_best_times.append(best_time)
        else:
            openmp_best_times.append(np.nan)

    ax1.loglog(problem_sizes, openmp_best_times, 's-', linewidth=3, markersize=8,
               label='OpenMP (best)', color=colors[1], alpha=0.8)

    # MPI时间（选择最佳进程数）
    mpi_data = df[df['Method'] == 'MPI']
    if not mpi_data.empty:
        mpi_best_times = []
        for size in problem_sizes:
            size_data = mpi_data[mpi_data['Problem_Size'] == size]
            if not size_data.empty:
                best_time = size_data['Time_Avg'].min()
                mpi_best_times.append(best_time)
            else:
                mpi_best_times.append(np.nan)

        ax1.loglog(problem_sizes, mpi_best_times, '^--', linewidth=3, markersize=8,
                   label='MPI (best)', color=colors[2], alpha=0.8)

    ax1.set_xlabel('Problem Size (intervals)')
    ax1.set_ylabel('Execution Time (seconds)')
    ax1.set_title('Performance Comparison: Best Configurations', fontweight='bold', pad=15)
    ax1.legend(frameon=True, fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.3, which='both')

    # 格式化x轴标签
    ax1.set_xticks(problem_sizes)
    ax1.set_xticklabels([f'{size/1e6:.0f}M' for size in problem_sizes])

    # 加速比对比（相对于串行）
    openmp_speedups = []
    mpi_speedups = []

    for i, size in enumerate(problem_sizes):
        serial_time = serial_times[i]

        # OpenMP最佳加速比
        openmp_best = openmp_best_times[i]
        if not np.isnan(openmp_best) and openmp_best > 0:
            openmp_speedups.append(serial_time / openmp_best)
        else:
            openmp_speedups.append(np.nan)

        # MPI最佳加速比
        if not mpi_data.empty and not np.isnan(mpi_best_times[i]) and mpi_best_times[i] > 0:
            mpi_speedups.append(serial_time / mpi_best_times[i])
        else:
            mpi_speedups.append(np.nan)

    x_pos = range(len(problem_sizes))
    width = 0.35

    ax2.bar([x - width/2 for x in x_pos], openmp_speedups, width,
            label='OpenMP', color=colors[1], alpha=0.8)

    if not mpi_data.empty:
        ax2.bar([x + width/2 for x in x_pos], mpi_speedups, width,
                label='MPI', color=colors[2], alpha=0.8)

    ax2.set_xlabel('Problem Size')
    ax2.set_ylabel('Best Speedup')
    ax2.set_title('Maximum Speedup Achieved', fontweight='bold', pad=15)
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels([f'{size/1e6:.0f}M' for size in problem_sizes])
    ax2.legend(frameon=True, fancybox=True, shadow=True)
    ax2.grid(True, alpha=0.3, axis='y')

    # 添加数值标签
    for i, (openmp_sp, mpi_sp) in enumerate(zip(openmp_speedups, mpi_speedups)):
        if not np.isnan(openmp_sp):
            ax2.text(i - width/2, openmp_sp + 0.5, f'{openmp_sp:.1f}x',
                    ha='center', va='bottom', fontweight='bold', fontsize=9)
        if not np.isnan(mpi_sp):
            ax2.text(i + width/2, mpi_sp + 0.01, f'{mpi_sp:.2f}x',
                    ha='center', va='bottom', fontweight='bold', fontsize=9)

    plt.tight_layout()
    plt.savefig('results/performance_comparison.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def plot_scalability_heatmap(df):
    """绘制可扩展性热图"""
    openmp_data = df[df['Method'] == 'OpenMP']

    # 创建数据透视表
    pivot_speedup = openmp_data.pivot(index='Threads', columns='Problem_Size', values='Speedup')
    pivot_efficiency = openmp_data.pivot(index='Threads', columns='Problem_Size', values='Speedup')

    # 计算效率
    for col in pivot_efficiency.columns:
        pivot_efficiency[col] = pivot_efficiency[col] / pivot_efficiency.index

    # 格式化列标签
    problem_size_labels = [f'{size/1e6:.0f}M' if size < 1e9 else f'{size/1e9:.1f}B'
                          for size in pivot_speedup.columns]
    pivot_speedup.columns = problem_size_labels
    pivot_efficiency.columns = problem_size_labels

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5.5))

    # 自定义配色方案
    speedup_cmap = sns.color_palette("rocket_r", as_cmap=True)
    efficiency_cmap = sns.color_palette("viridis", as_cmap=True)

    # 加速比热图
    sns.heatmap(pivot_speedup, annot=True, fmt='.1f', cmap=speedup_cmap,
                ax=ax1, cbar_kws={'label': 'Speedup', 'shrink': 0.8},
                linewidths=0.5, linecolor='white',
                annot_kws={'size': 9, 'weight': 'bold'})
    ax1.set_title('Speedup Performance Matrix', fontweight='bold', pad=15)
    ax1.set_xlabel('Problem Size')
    ax1.set_ylabel('Number of Threads')
    ax1.tick_params(axis='x', rotation=45)

    # 效率热图
    sns.heatmap(pivot_efficiency, annot=True, fmt='.2f', cmap=efficiency_cmap,
                ax=ax2, cbar_kws={'label': 'Parallel Efficiency', 'shrink': 0.8},
                vmin=0, vmax=1.2, linewidths=0.5, linecolor='white',
                annot_kws={'size': 9, 'weight': 'bold'})
    ax2.set_title('Parallel Efficiency Matrix', fontweight='bold', pad=15)
    ax2.set_xlabel('Problem Size')
    ax2.set_ylabel('Number of Threads')
    ax2.tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.savefig('results/scalability_heatmap.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def generate_summary_stats(df):
    """生成统计摘要"""
    with open('results/performance_summary.txt', 'w') as f:
        f.write("π值计算并行性能分析摘要\n")
        f.write("=" * 40 + "\n\n")
        
        # 最佳性能
        openmp_data = df[df['Method'] == 'OpenMP']
        best_speedup = openmp_data['Speedup'].max()
        best_speedup_row = openmp_data[openmp_data['Speedup'] == best_speedup].iloc[0]
        
        f.write(f"最佳加速比: {best_speedup:.2f}x\n")
        f.write(f"  - 问题规模: {best_speedup_row['Problem_Size']:,} 个积分区间\n")
        f.write(f"  - 线程数: {best_speedup_row['Threads']}\n")
        f.write(f"  - 执行时间: {best_speedup_row['Time_Avg']:.6f} 秒\n\n")
        
        # 效率分析
        max_threads = openmp_data['Threads'].max()
        max_thread_data = openmp_data[openmp_data['Threads'] == max_threads]
        avg_efficiency = (max_thread_data['Speedup'] / max_threads).mean()
        
        f.write(f"平均并行效率 ({max_threads}线程): {avg_efficiency:.3f}\n\n")
        
        # 精度分析
        serial_data = df[df['Method'] == 'Serial']
        avg_error = serial_data['Error'].mean()
        f.write(f"平均计算误差: {avg_error:.2e}\n")

def plot_three_method_comparison(df):
    """绘制三种方法的详细对比图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    problem_sizes = sorted(df['Problem_Size'].unique())
    serial_data = df[df['Method'] == 'Serial']
    openmp_data = df[df['Method'] == 'OpenMP']
    mpi_data = df[df['Method'] == 'MPI']

    # 1. 执行时间对比 (左上)
    for size in problem_sizes:
        # 串行时间
        serial_time = serial_data[serial_data['Problem_Size'] == size]['Time_Avg'].iloc[0]

        # OpenMP时间
        openmp_size = openmp_data[openmp_data['Problem_Size'] == size]
        if not openmp_size.empty:
            ax1.plot(openmp_size['Threads'], openmp_size['Time_Avg'],
                    'o-', linewidth=2, markersize=6,
                    label=f'OpenMP {size/1e6:.0f}M', alpha=0.8)

        # MPI时间
        mpi_size = mpi_data[mpi_data['Problem_Size'] == size]
        if not mpi_size.empty:
            ax1.plot(mpi_size['Threads'], mpi_size['Time_Avg'],
                    's--', linewidth=2, markersize=6,
                    label=f'MPI {size/1e6:.0f}M', alpha=0.8)

        # 串行基准线
        ax1.axhline(y=serial_time, color='gray', linestyle=':', alpha=0.5)

    ax1.set_xlabel('Threads/Processes')
    ax1.set_ylabel('Execution Time (s)')
    ax1.set_title('Execution Time Comparison', fontweight='bold')
    ax1.set_yscale('log')
    ax1.legend(fontsize=8, ncol=2)
    ax1.grid(True, alpha=0.3)

    # 2. 加速比对比 (右上)
    for size in problem_sizes:
        openmp_size = openmp_data[openmp_data['Problem_Size'] == size]
        if not openmp_size.empty:
            ax2.plot(openmp_size['Threads'], openmp_size['Speedup'],
                    'o-', linewidth=2, markersize=6,
                    label=f'OpenMP {size/1e6:.0f}M', alpha=0.8)

        mpi_size = mpi_data[mpi_data['Problem_Size'] == size]
        if not mpi_size.empty:
            ax2.plot(mpi_size['Threads'], mpi_size['Speedup'],
                    's--', linewidth=2, markersize=6,
                    label=f'MPI {size/1e6:.0f}M', alpha=0.8)

    # 理想加速比
    max_threads = max(df['Threads'].max(), 16)
    ax2.plot(range(1, max_threads + 1), range(1, max_threads + 1),
             'k--', alpha=0.5, label='Ideal')

    ax2.set_xlabel('Threads/Processes')
    ax2.set_ylabel('Speedup')
    ax2.set_title('Speedup Comparison', fontweight='bold')
    ax2.legend(fontsize=8, ncol=2)
    ax2.grid(True, alpha=0.3)

    # 3. 效率对比 (左下)
    for size in problem_sizes:
        openmp_size = openmp_data[openmp_data['Problem_Size'] == size]
        if not openmp_size.empty:
            efficiency = openmp_size['Speedup'] / openmp_size['Threads']
            ax3.plot(openmp_size['Threads'], efficiency,
                    'o-', linewidth=2, markersize=6,
                    label=f'OpenMP {size/1e6:.0f}M', alpha=0.8)

        mpi_size = mpi_data[mpi_data['Problem_Size'] == size]
        if not mpi_size.empty:
            efficiency = mpi_size['Speedup'] / mpi_size['Threads']
            ax3.plot(mpi_size['Threads'], efficiency,
                    's--', linewidth=2, markersize=6,
                    label=f'MPI {size/1e6:.0f}M', alpha=0.8)

    ax3.axhline(y=1.0, color='gray', linestyle='--', alpha=0.7)
    ax3.set_xlabel('Threads/Processes')
    ax3.set_ylabel('Parallel Efficiency')
    ax3.set_title('Parallel Efficiency Comparison', fontweight='bold')
    ax3.legend(fontsize=8, ncol=2)
    ax3.grid(True, alpha=0.3)
    ax3.set_ylim(0, 1.2)

    # 4. 最佳性能总结 (右下)
    methods = ['Serial', 'OpenMP\n(Best)', 'MPI\n(Best)']
    best_times = []
    best_speedups = []

    # 选择最大问题规模进行对比
    largest_size = max(problem_sizes)

    # 串行时间
    serial_time = serial_data[serial_data['Problem_Size'] == largest_size]['Time_Avg'].iloc[0]
    best_times.append(serial_time)
    best_speedups.append(1.0)

    # OpenMP最佳时间
    openmp_largest = openmp_data[openmp_data['Problem_Size'] == largest_size]
    if not openmp_largest.empty:
        openmp_best_time = openmp_largest['Time_Avg'].min()
        openmp_best_speedup = openmp_largest['Speedup'].max()
        best_times.append(openmp_best_time)
        best_speedups.append(openmp_best_speedup)
    else:
        best_times.append(serial_time)
        best_speedups.append(1.0)

    # MPI最佳时间
    mpi_largest = mpi_data[mpi_data['Problem_Size'] == largest_size]
    if not mpi_largest.empty:
        mpi_best_time = mpi_largest['Time_Avg'].min()
        mpi_best_speedup = mpi_largest['Speedup'].max()
        best_times.append(mpi_best_time)
        best_speedups.append(mpi_best_speedup)
    else:
        best_times.append(serial_time)
        best_speedups.append(1.0)

    x_pos = range(len(methods))
    bars = ax4.bar(x_pos, best_times, color=[colors[0], colors[1], colors[2]], alpha=0.8)

    # 添加数值标签
    for i, (bar, time, speedup) in enumerate(zip(bars, best_times, best_speedups)):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{time:.3f}s\n({speedup:.1f}x)',
                ha='center', va='bottom', fontweight='bold', fontsize=10)

    ax4.set_xlabel('Method')
    ax4.set_ylabel('Best Execution Time (s)')
    ax4.set_title(f'Best Performance Summary ({largest_size/1e6:.0f}M intervals)', fontweight='bold')
    ax4.set_xticks(x_pos)
    ax4.set_xticklabels(methods)
    ax4.set_yscale('log')
    ax4.grid(True, alpha=0.3, axis='y')

    plt.suptitle('Comprehensive Performance Analysis: Serial vs OpenMP vs MPI',
                fontsize=16, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)

    plt.savefig('results/three_method_comparison.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def plot_comprehensive_analysis(df):
    """绘制综合性能分析图"""
    fig = plt.figure(figsize=(16, 10))

    # 创建子图布局
    gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)

    openmp_data = df[df['Method'] == 'OpenMP']
    serial_data = df[df['Method'] == 'Serial']
    problem_sizes = sorted(openmp_data['Problem_Size'].unique())

    # 1. 加速比趋势 (左上)
    ax1 = fig.add_subplot(gs[0, 0])
    for i, size in enumerate(problem_sizes[:3]):  # 只显示前3个规模
        size_data = openmp_data[openmp_data['Problem_Size'] == size]
        label = f'{size/1e6:.0f}M'
        ax1.plot(size_data['Threads'], size_data['Speedup'],
                'o-', linewidth=2, markersize=5, label=label, color=colors[i])

    max_threads = openmp_data['Threads'].max()
    ax1.plot(range(1, max_threads + 1), range(1, max_threads + 1),
             '--', color='gray', alpha=0.7, label='Ideal')
    ax1.set_xlabel('Threads')
    ax1.set_ylabel('Speedup')
    ax1.set_title('Speedup Trends', fontweight='bold')
    ax1.legend(fontsize=8)
    ax1.grid(True, alpha=0.3)

    # 2. 执行时间对比 (中上)
    ax2 = fig.add_subplot(gs[0, 1])
    serial_times = [serial_data[serial_data['Problem_Size'] == size]['Time_Avg'].iloc[0]
                   for size in problem_sizes]
    openmp_16_times = []
    for size in problem_sizes:
        size_data = openmp_data[(openmp_data['Problem_Size'] == size) &
                               (openmp_data['Threads'] == 16)]
        if not size_data.empty:
            openmp_16_times.append(size_data['Time_Avg'].iloc[0])
        else:
            openmp_16_times.append(np.nan)

    x_pos = range(len(problem_sizes))
    width = 0.35
    ax2.bar([x - width/2 for x in x_pos], serial_times, width,
            label='Serial', color=colors[0], alpha=0.8)
    ax2.bar([x + width/2 for x in x_pos], openmp_16_times, width,
            label='OpenMP (16T)', color=colors[1], alpha=0.8)

    ax2.set_xlabel('Problem Size')
    ax2.set_ylabel('Time (s)')
    ax2.set_title('Execution Time Comparison', fontweight='bold')
    ax2.set_yscale('log')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels([f'{size/1e6:.0f}M' for size in problem_sizes], rotation=45)
    ax2.legend(fontsize=8)
    ax2.grid(True, alpha=0.3)

    # 3. 并行效率 (右上)
    ax3 = fig.add_subplot(gs[0, 2])
    thread_counts = sorted(openmp_data['Threads'].unique())
    avg_efficiency = []
    for threads in thread_counts:
        thread_data = openmp_data[openmp_data['Threads'] == threads]
        efficiency = (thread_data['Speedup'] / threads).mean()
        avg_efficiency.append(efficiency)

    ax3.plot(thread_counts, avg_efficiency, 'o-', linewidth=3, markersize=8,
             color=colors[2], alpha=0.8)
    ax3.axhline(y=1.0, color='gray', linestyle='--', alpha=0.7)
    ax3.set_xlabel('Threads')
    ax3.set_ylabel('Avg Efficiency')
    ax3.set_title('Average Parallel Efficiency', fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.set_ylim(0, max(avg_efficiency) * 1.1)

    # 4. 最佳配置分析 (下方大图)
    ax4 = fig.add_subplot(gs[1:, :])

    # 创建性能矩阵
    pivot_data = openmp_data.pivot(index='Threads', columns='Problem_Size', values='Speedup')

    # 格式化标签
    col_labels = [f'{size/1e6:.0f}M' if size < 1e9 else f'{size/1e9:.1f}B'
                 for size in pivot_data.columns]
    pivot_data.columns = col_labels

    # 绘制热图
    im = ax4.imshow(pivot_data.values, cmap='plasma', aspect='auto', interpolation='nearest')

    # 添加数值标注
    for i in range(len(pivot_data.index)):
        for j in range(len(pivot_data.columns)):
            value = pivot_data.iloc[i, j]
            if not np.isnan(value):
                ax4.text(j, i, f'{value:.1f}', ha='center', va='center',
                        color='white' if value > pivot_data.values.max()*0.6 else 'black',
                        fontweight='bold', fontsize=10)

    ax4.set_xticks(range(len(col_labels)))
    ax4.set_xticklabels(col_labels)
    ax4.set_yticks(range(len(pivot_data.index)))
    ax4.set_yticklabels(pivot_data.index)
    ax4.set_xlabel('Problem Size')
    ax4.set_ylabel('Number of Threads')
    ax4.set_title('Speedup Performance Matrix', fontweight='bold', pad=20)

    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax4, shrink=0.8, aspect=30)
    cbar.set_label('Speedup', rotation=270, labelpad=20)

    plt.suptitle('π Calculation Parallel Performance Analysis',
                fontsize=16, fontweight='bold', y=0.98)

    plt.savefig('results/comprehensive_analysis.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def main():
    """主函数"""
    print("生成性能分析图表...")

    # 创建结果目录
    Path("results").mkdir(exist_ok=True)

    # 加载数据
    df = load_data()
    if df is None:
        return

    print("数据加载完成，开始生成图表...")

    # 生成各种图表
    plot_speedup_analysis(df)
    print("✓ 加速比分析图已生成")

    plot_performance_comparison(df)
    print("✓ 性能对比图已生成")

    plot_three_method_comparison(df)
    print("✓ 三方法对比图已生成")

    plot_scalability_heatmap(df)
    print("✓ 可扩展性热图已生成")

    plot_comprehensive_analysis(df)
    print("✓ 综合性能分析图已生成")

    generate_summary_stats(df)
    print("✓ 性能摘要已生成")

    print("\n所有图表已保存到 results/ 目录")

if __name__ == "__main__":
    main()
