#!/usr/bin/env python3
"""
数据可视化脚本
生成性能分析图表
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from pathlib import Path

# 设置高质量图表样式
plt.rcParams.update({
    'font.family': 'serif',
    'font.serif': ['Times New Roman', 'DejaVu Serif'],
    'font.size': 11,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.titlesize': 16,
    'axes.unicode_minus': False,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

# 设置专业配色方案
colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83', '#1B998B']
sns.set_palette(colors)

def load_data():
    """加载性能数据"""
    data_file = Path("results/performance_data.csv")
    if not data_file.exists():
        print("错误: 未找到性能数据文件，请先运行基准测试")
        return None

    df = pd.read_csv(data_file)
    return df

def plot_speedup_analysis(df):
    """绘制加速比分析图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5.5))

    # 过滤OpenMP数据
    openmp_data = df[df['Method'] == 'OpenMP']

    # 加速比随线程数变化
    problem_sizes = sorted(openmp_data['Problem_Size'].unique())
    markers = ['o', 's', '^', 'D', 'v']

    for i, size in enumerate(problem_sizes):
        size_data = openmp_data[openmp_data['Problem_Size'] == size]
        label = f'{size/1e6:.0f}M' if size < 1e9 else f'{size/1e9:.1f}B'
        ax1.plot(size_data['Threads'], size_data['Speedup'],
                marker=markers[i % len(markers)], linewidth=2.5, markersize=6,
                label=label, alpha=0.8)

    # 理想加速比线
    max_threads = openmp_data['Threads'].max()
    ideal_threads = range(1, max_threads + 1)
    ax1.plot(ideal_threads, ideal_threads, '--', color='#666666',
             linewidth=2, alpha=0.7, label='Ideal Speedup')

    ax1.set_xlabel('Number of Threads')
    ax1.set_ylabel('Speedup')
    ax1.set_title('Speedup Analysis', fontweight='bold', pad=15)
    ax1.legend(frameon=True, fancybox=True, shadow=True, ncol=2,
               bbox_to_anchor=(0.5, -0.15), loc='upper center')
    ax1.set_xlim(0.5, max_threads + 0.5)
    ax1.set_ylim(0, max(openmp_data['Speedup'].max() * 1.1, max_threads * 1.1))

    # 效率分析
    for i, size in enumerate(problem_sizes):
        size_data = openmp_data[openmp_data['Problem_Size'] == size]
        efficiency = size_data['Speedup'] / size_data['Threads']
        label = f'{size/1e6:.0f}M' if size < 1e9 else f'{size/1e9:.1f}B'
        ax2.plot(size_data['Threads'], efficiency,
                marker=markers[i % len(markers)], linewidth=2.5, markersize=6,
                label=label, alpha=0.8)

    # 100%效率参考线
    ax2.axhline(y=1.0, color='#666666', linestyle='--', linewidth=2, alpha=0.7, label='100% Efficiency')

    ax2.set_xlabel('Number of Threads')
    ax2.set_ylabel('Parallel Efficiency')
    ax2.set_title('Parallel Efficiency Analysis', fontweight='bold', pad=15)
    ax2.legend(frameon=True, fancybox=True, shadow=True, ncol=2,
               bbox_to_anchor=(0.5, -0.15), loc='upper center')
    ax2.set_xlim(0.5, max_threads + 0.5)
    ax2.set_ylim(0, max(efficiency.max() * 1.1, 1.3))

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.2)  # 为图例留出空间
    plt.savefig('results/speedup_analysis.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def plot_performance_comparison(df):
    """绘制性能对比图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5.5))

    # 执行时间对比
    problem_sizes = sorted(df['Problem_Size'].unique())

    # 串行时间
    serial_data = df[df['Method'] == 'Serial']
    serial_times = [serial_data[serial_data['Problem_Size'] == size]['Time_Avg'].iloc[0]
                   for size in problem_sizes]

    ax1.loglog(problem_sizes, serial_times, 'o-', linewidth=3, markersize=8,
               label='Serial', color=colors[0], alpha=0.8)

    # 不同线程数的OpenMP时间
    thread_configs = [2, 4, 8, 16]
    markers = ['s', '^', 'D', 'v']

    for i, threads in enumerate(thread_configs):
        openmp_data = df[(df['Method'] == 'OpenMP') & (df['Threads'] == threads)]
        if not openmp_data.empty:
            openmp_times = []
            for size in problem_sizes:
                size_data = openmp_data[openmp_data['Problem_Size'] == size]
                if not size_data.empty:
                    openmp_times.append(size_data['Time_Avg'].iloc[0])
                else:
                    openmp_times.append(np.nan)

            ax1.loglog(problem_sizes, openmp_times, markers[i] + '-',
                      linewidth=2.5, markersize=7, alpha=0.8,
                      label=f'OpenMP ({threads} threads)', color=colors[i+1])

    ax1.set_xlabel('Problem Size (intervals)')
    ax1.set_ylabel('Execution Time (seconds)')
    ax1.set_title('Performance Scaling', fontweight='bold', pad=15)
    ax1.legend(frameon=True, fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.3, which='both')

    # 格式化x轴标签
    ax1.set_xticks(problem_sizes)
    ax1.set_xticklabels([f'{size/1e6:.0f}M' if size < 1e9 else f'{size/1e9:.1f}B'
                        for size in problem_sizes])

    # 计算精度分析
    serial_data = df[df['Method'] == 'Serial']
    ax2.semilogx(serial_data['Problem_Size'], serial_data['Error'],
                'o-', linewidth=3, markersize=8, label='Serial',
                color=colors[0], alpha=0.8)

    # OpenMP 16线程的精度
    openmp_16 = df[(df['Method'] == 'OpenMP') & (df['Threads'] == 16)]
    if not openmp_16.empty:
        ax2.semilogx(openmp_16['Problem_Size'], openmp_16['Error'],
                    's-', linewidth=3, markersize=8,
                    label='OpenMP (16 threads)', color=colors[4], alpha=0.8)

    ax2.set_xlabel('Problem Size (intervals)')
    ax2.set_ylabel('Absolute Error')
    ax2.set_title('Computational Accuracy', fontweight='bold', pad=15)
    ax2.legend(frameon=True, fancybox=True, shadow=True)
    ax2.grid(True, alpha=0.3, which='both')
    ax2.set_yscale('log')

    # 格式化x轴标签
    ax2.set_xticks(problem_sizes)
    ax2.set_xticklabels([f'{size/1e6:.0f}M' if size < 1e9 else f'{size/1e9:.1f}B'
                        for size in problem_sizes])

    plt.tight_layout()
    plt.savefig('results/performance_comparison.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def plot_scalability_heatmap(df):
    """绘制可扩展性热图"""
    openmp_data = df[df['Method'] == 'OpenMP']

    # 创建数据透视表
    pivot_speedup = openmp_data.pivot(index='Threads', columns='Problem_Size', values='Speedup')
    pivot_efficiency = openmp_data.pivot(index='Threads', columns='Problem_Size', values='Speedup')

    # 计算效率
    for col in pivot_efficiency.columns:
        pivot_efficiency[col] = pivot_efficiency[col] / pivot_efficiency.index

    # 格式化列标签
    problem_size_labels = [f'{size/1e6:.0f}M' if size < 1e9 else f'{size/1e9:.1f}B'
                          for size in pivot_speedup.columns]
    pivot_speedup.columns = problem_size_labels
    pivot_efficiency.columns = problem_size_labels

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5.5))

    # 自定义配色方案
    speedup_cmap = sns.color_palette("rocket_r", as_cmap=True)
    efficiency_cmap = sns.color_palette("viridis", as_cmap=True)

    # 加速比热图
    sns.heatmap(pivot_speedup, annot=True, fmt='.1f', cmap=speedup_cmap,
                ax=ax1, cbar_kws={'label': 'Speedup', 'shrink': 0.8},
                linewidths=0.5, linecolor='white',
                annot_kws={'size': 9, 'weight': 'bold'})
    ax1.set_title('Speedup Performance Matrix', fontweight='bold', pad=15)
    ax1.set_xlabel('Problem Size')
    ax1.set_ylabel('Number of Threads')
    ax1.tick_params(axis='x', rotation=45)

    # 效率热图
    sns.heatmap(pivot_efficiency, annot=True, fmt='.2f', cmap=efficiency_cmap,
                ax=ax2, cbar_kws={'label': 'Parallel Efficiency', 'shrink': 0.8},
                vmin=0, vmax=1.2, linewidths=0.5, linecolor='white',
                annot_kws={'size': 9, 'weight': 'bold'})
    ax2.set_title('Parallel Efficiency Matrix', fontweight='bold', pad=15)
    ax2.set_xlabel('Problem Size')
    ax2.set_ylabel('Number of Threads')
    ax2.tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.savefig('results/scalability_heatmap.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def generate_summary_stats(df):
    """生成统计摘要"""
    with open('results/performance_summary.txt', 'w') as f:
        f.write("π值计算并行性能分析摘要\n")
        f.write("=" * 40 + "\n\n")
        
        # 最佳性能
        openmp_data = df[df['Method'] == 'OpenMP']
        best_speedup = openmp_data['Speedup'].max()
        best_speedup_row = openmp_data[openmp_data['Speedup'] == best_speedup].iloc[0]
        
        f.write(f"最佳加速比: {best_speedup:.2f}x\n")
        f.write(f"  - 问题规模: {best_speedup_row['Problem_Size']:,} 个积分区间\n")
        f.write(f"  - 线程数: {best_speedup_row['Threads']}\n")
        f.write(f"  - 执行时间: {best_speedup_row['Time_Avg']:.6f} 秒\n\n")
        
        # 效率分析
        max_threads = openmp_data['Threads'].max()
        max_thread_data = openmp_data[openmp_data['Threads'] == max_threads]
        avg_efficiency = (max_thread_data['Speedup'] / max_threads).mean()
        
        f.write(f"平均并行效率 ({max_threads}线程): {avg_efficiency:.3f}\n\n")
        
        # 精度分析
        serial_data = df[df['Method'] == 'Serial']
        avg_error = serial_data['Error'].mean()
        f.write(f"平均计算误差: {avg_error:.2e}\n")

def plot_comprehensive_analysis(df):
    """绘制综合性能分析图"""
    fig = plt.figure(figsize=(16, 10))

    # 创建子图布局
    gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)

    openmp_data = df[df['Method'] == 'OpenMP']
    serial_data = df[df['Method'] == 'Serial']
    problem_sizes = sorted(openmp_data['Problem_Size'].unique())

    # 1. 加速比趋势 (左上)
    ax1 = fig.add_subplot(gs[0, 0])
    for i, size in enumerate(problem_sizes[:3]):  # 只显示前3个规模
        size_data = openmp_data[openmp_data['Problem_Size'] == size]
        label = f'{size/1e6:.0f}M'
        ax1.plot(size_data['Threads'], size_data['Speedup'],
                'o-', linewidth=2, markersize=5, label=label, color=colors[i])

    max_threads = openmp_data['Threads'].max()
    ax1.plot(range(1, max_threads + 1), range(1, max_threads + 1),
             '--', color='gray', alpha=0.7, label='Ideal')
    ax1.set_xlabel('Threads')
    ax1.set_ylabel('Speedup')
    ax1.set_title('Speedup Trends', fontweight='bold')
    ax1.legend(fontsize=8)
    ax1.grid(True, alpha=0.3)

    # 2. 执行时间对比 (中上)
    ax2 = fig.add_subplot(gs[0, 1])
    serial_times = [serial_data[serial_data['Problem_Size'] == size]['Time_Avg'].iloc[0]
                   for size in problem_sizes]
    openmp_16_times = []
    for size in problem_sizes:
        size_data = openmp_data[(openmp_data['Problem_Size'] == size) &
                               (openmp_data['Threads'] == 16)]
        if not size_data.empty:
            openmp_16_times.append(size_data['Time_Avg'].iloc[0])
        else:
            openmp_16_times.append(np.nan)

    x_pos = range(len(problem_sizes))
    width = 0.35
    ax2.bar([x - width/2 for x in x_pos], serial_times, width,
            label='Serial', color=colors[0], alpha=0.8)
    ax2.bar([x + width/2 for x in x_pos], openmp_16_times, width,
            label='OpenMP (16T)', color=colors[1], alpha=0.8)

    ax2.set_xlabel('Problem Size')
    ax2.set_ylabel('Time (s)')
    ax2.set_title('Execution Time Comparison', fontweight='bold')
    ax2.set_yscale('log')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels([f'{size/1e6:.0f}M' for size in problem_sizes], rotation=45)
    ax2.legend(fontsize=8)
    ax2.grid(True, alpha=0.3)

    # 3. 并行效率 (右上)
    ax3 = fig.add_subplot(gs[0, 2])
    thread_counts = sorted(openmp_data['Threads'].unique())
    avg_efficiency = []
    for threads in thread_counts:
        thread_data = openmp_data[openmp_data['Threads'] == threads]
        efficiency = (thread_data['Speedup'] / threads).mean()
        avg_efficiency.append(efficiency)

    ax3.plot(thread_counts, avg_efficiency, 'o-', linewidth=3, markersize=8,
             color=colors[2], alpha=0.8)
    ax3.axhline(y=1.0, color='gray', linestyle='--', alpha=0.7)
    ax3.set_xlabel('Threads')
    ax3.set_ylabel('Avg Efficiency')
    ax3.set_title('Average Parallel Efficiency', fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.set_ylim(0, max(avg_efficiency) * 1.1)

    # 4. 最佳配置分析 (下方大图)
    ax4 = fig.add_subplot(gs[1:, :])

    # 创建性能矩阵
    pivot_data = openmp_data.pivot(index='Threads', columns='Problem_Size', values='Speedup')

    # 格式化标签
    col_labels = [f'{size/1e6:.0f}M' if size < 1e9 else f'{size/1e9:.1f}B'
                 for size in pivot_data.columns]
    pivot_data.columns = col_labels

    # 绘制热图
    im = ax4.imshow(pivot_data.values, cmap='plasma', aspect='auto', interpolation='nearest')

    # 添加数值标注
    for i in range(len(pivot_data.index)):
        for j in range(len(pivot_data.columns)):
            value = pivot_data.iloc[i, j]
            if not np.isnan(value):
                ax4.text(j, i, f'{value:.1f}', ha='center', va='center',
                        color='white' if value > pivot_data.values.max()*0.6 else 'black',
                        fontweight='bold', fontsize=10)

    ax4.set_xticks(range(len(col_labels)))
    ax4.set_xticklabels(col_labels)
    ax4.set_yticks(range(len(pivot_data.index)))
    ax4.set_yticklabels(pivot_data.index)
    ax4.set_xlabel('Problem Size')
    ax4.set_ylabel('Number of Threads')
    ax4.set_title('Speedup Performance Matrix', fontweight='bold', pad=20)

    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax4, shrink=0.8, aspect=30)
    cbar.set_label('Speedup', rotation=270, labelpad=20)

    plt.suptitle('π Calculation Parallel Performance Analysis',
                fontsize=16, fontweight='bold', y=0.98)

    plt.savefig('results/comprehensive_analysis.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.close()

def main():
    """主函数"""
    print("生成性能分析图表...")

    # 创建结果目录
    Path("results").mkdir(exist_ok=True)

    # 加载数据
    df = load_data()
    if df is None:
        return

    print("数据加载完成，开始生成图表...")

    # 生成各种图表
    plot_speedup_analysis(df)
    print("✓ 加速比分析图已生成")

    plot_performance_comparison(df)
    print("✓ 性能对比图已生成")

    plot_scalability_heatmap(df)
    print("✓ 可扩展性热图已生成")

    plot_comprehensive_analysis(df)
    print("✓ 综合性能分析图已生成")

    generate_summary_stats(df)
    print("✓ 性能摘要已生成")

    print("\n所有图表已保存到 results/ 目录")

if __name__ == "__main__":
    main()
