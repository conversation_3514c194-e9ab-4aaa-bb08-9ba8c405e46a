#include "dot_product.h"
#include <mpi.h>
#include <iostream>
#include <vector>

/**
 * MPI并行点积计算主程序
 * 
 * 本程序专门用于测试MPI分布式并行计算的性能表现。
 * 支持多进程环境下的向量点积运算，并提供详细的
 * 性能分析和通信开销评估。
 */

void print_mpi_info(int rank, int size) {
    if (rank == 0) {
        std::cout << "========================================" << std::endl;
        std::cout << "    MPI分布式点积计算性能测试" << std::endl;
        std::cout << "========================================" << std::endl;
        std::cout << "MPI配置信息:" << std::endl;
        std::cout << "  总进程数: " << size << std::endl;
        std::cout << "  主进程编号: " << rank << std::endl;
        std::cout << "========================================" << std::endl;
    }
    
    // 每个进程报告自己的信息
    for (int i = 0; i < size; ++i) {
        if (rank == i) {
            std::cout << "进程 " << rank << " 已就绪" << std::endl;
        }
        MPI_Barrier(MPI_COMM_WORLD);
    }
    
    if (rank == 0) {
        std::cout << std::endl;
    }
}

void test_mpi_basic_functionality(int rank, int size) {
    if (rank == 0) {
        std::cout << "=== MPI基础功能测试 ===" << std::endl;
    }
    
    const int test_size = 1000;
    Vector a, b;
    
    // 主进程生成测试数据
    if (rank == 0) {
        a = generate_random_vector(test_size, 1.0, 5.0);
        b = generate_random_vector(test_size, 1.0, 5.0);
        
        std::cout << "生成测试向量，大小: " << test_size << std::endl;
        
        // 串行计算作为参考
        double serial_result = serial_dot_product(a, b);
        std::cout << "串行计算结果: " << std::fixed << std::setprecision(10) 
                  << serial_result << std::endl;
    }
    
    // 广播向量数据到所有进程
    if (rank != 0) {
        a.resize(test_size);
        b.resize(test_size);
    }
    
    MPI_Bcast(a.data(), test_size, MPI_DOUBLE, 0, MPI_COMM_WORLD);
    MPI_Bcast(b.data(), test_size, MPI_DOUBLE, 0, MPI_COMM_WORLD);
    
    // MPI并行计算
    MPI_Barrier(MPI_COMM_WORLD);
    auto start = std::chrono::high_resolution_clock::now();
    
    double mpi_result = mpi_dot_product(a, b, rank, size);
    
    MPI_Barrier(MPI_COMM_WORLD);
    auto end = std::chrono::high_resolution_clock::now();
    
    if (rank == 0) {
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        double mpi_time = duration.count() / 1000.0;
        
        std::cout << "MPI计算结果: " << std::fixed << std::setprecision(10) 
                  << mpi_result << std::endl;
        std::cout << "MPI计算时间: " << std::setprecision(3) 
                  << mpi_time << " ms" << std::endl;
        std::cout << std::endl;
    }
}

void test_mpi_scalability(int rank, int size) {
    if (rank == 0) {
        std::cout << "=== MPI可扩展性测试 ===" << std::endl;
    }
    
    std::vector<int> test_sizes = {10000, 100000, 1000000, 10000000};
    
    for (int vector_size : test_sizes) {
        Vector a, b;
        
        // 主进程生成数据
        if (rank == 0) {
            std::cout << "\n测试向量大小: " << vector_size << std::endl;
            std::cout << std::string(40, '-') << std::endl;
            
            a = generate_random_vector(vector_size);
            b = generate_random_vector(vector_size);
            
            // 串行基准测试
            auto serial_start = std::chrono::high_resolution_clock::now();
            double serial_result = serial_dot_product(a, b);
            auto serial_end = std::chrono::high_resolution_clock::now();
            auto serial_duration = std::chrono::duration_cast<std::chrono::microseconds>(serial_end - serial_start);
            double serial_time = serial_duration.count() / 1000.0;
            
            std::cout << "串行时间: " << std::fixed << std::setprecision(3) 
                      << serial_time << " ms" << std::endl;
        }
        
        // 广播数据
        if (rank != 0) {
            a.resize(vector_size);
            b.resize(vector_size);
        }
        
        MPI_Bcast(a.data(), vector_size, MPI_DOUBLE, 0, MPI_COMM_WORLD);
        MPI_Bcast(b.data(), vector_size, MPI_DOUBLE, 0, MPI_COMM_WORLD);
        
        // MPI性能测试
        MPI_Barrier(MPI_COMM_WORLD);
        auto mpi_start = std::chrono::high_resolution_clock::now();
        
        double mpi_result = mpi_dot_product(a, b, rank, size);
        
        MPI_Barrier(MPI_COMM_WORLD);
        auto mpi_end = std::chrono::high_resolution_clock::now();
        
        if (rank == 0) {
            auto mpi_duration = std::chrono::duration_cast<std::chrono::microseconds>(mpi_end - mpi_start);
            double mpi_time = mpi_duration.count() / 1000.0;
            
            std::cout << "MPI时间: " << std::setprecision(3) << mpi_time << " ms" << std::endl;
            std::cout << "MPI结果: " << std::setprecision(10) << mpi_result << std::endl;
        }
        
        // 详细性能分析
        analyze_mpi_performance(a, b, rank, size);
    }
}

void test_communication_overhead(int rank, int size) {
    if (rank == 0) {
        std::cout << "=== MPI通信开销分析 ===" << std::endl;
    }
    
    std::vector<int> data_sizes = {1000, 10000, 100000, 1000000};
    
    for (int data_size : data_sizes) {
        Vector test_data;
        
        if (rank == 0) {
            test_data = generate_random_vector(data_size);
            std::cout << "\n数据大小: " << data_size << " 元素" << std::endl;
        } else {
            test_data.resize(data_size);
        }
        
        // 测试广播时间
        MPI_Barrier(MPI_COMM_WORLD);
        auto bcast_start = std::chrono::high_resolution_clock::now();
        
        MPI_Bcast(test_data.data(), data_size, MPI_DOUBLE, 0, MPI_COMM_WORLD);
        
        MPI_Barrier(MPI_COMM_WORLD);
        auto bcast_end = std::chrono::high_resolution_clock::now();
        
        // 测试归约时间
        double local_sum = 0.0;
        for (double val : test_data) {
            local_sum += val;
        }
        
        MPI_Barrier(MPI_COMM_WORLD);
        auto reduce_start = std::chrono::high_resolution_clock::now();
        
        double global_sum;
        MPI_Reduce(&local_sum, &global_sum, 1, MPI_DOUBLE, MPI_SUM, 0, MPI_COMM_WORLD);
        
        MPI_Barrier(MPI_COMM_WORLD);
        auto reduce_end = std::chrono::high_resolution_clock::now();
        
        if (rank == 0) {
            auto bcast_duration = std::chrono::duration_cast<std::chrono::microseconds>(bcast_end - bcast_start);
            auto reduce_duration = std::chrono::duration_cast<std::chrono::microseconds>(reduce_end - reduce_start);
            
            double bcast_time = bcast_duration.count() / 1000.0;
            double reduce_time = reduce_duration.count() / 1000.0;
            
            std::cout << "  广播时间: " << std::fixed << std::setprecision(3) 
                      << bcast_time << " ms" << std::endl;
            std::cout << "  归约时间: " << reduce_time << " ms" << std::endl;
            std::cout << "  总通信时间: " << bcast_time + reduce_time << " ms" << std::endl;
            
            double data_mb = data_size * sizeof(double) / (1024.0 * 1024.0);
            std::cout << "  数据量: " << std::setprecision(2) << data_mb << " MB" << std::endl;
            std::cout << "  带宽: " << data_mb / (bcast_time / 1000.0) << " MB/s" << std::endl;
        }
    }
}

int main(int argc, char* argv[]) {
    MPI_Init(&argc, &argv);
    
    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);
    
    print_mpi_info(rank, size);
    
    // 基础功能测试
    test_mpi_basic_functionality(rank, size);
    
    // 可扩展性测试
    test_mpi_scalability(rank, size);
    
    // 通信开销分析
    test_communication_overhead(rank, size);
    
    if (rank == 0) {
        std::cout << "\n=== MPI测试完成 ===" << std::endl;
    }
    
    MPI_Finalize();
    return 0;
}
