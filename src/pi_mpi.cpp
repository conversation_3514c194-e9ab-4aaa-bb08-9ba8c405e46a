/*
 * MPI并行版本：使用数值积分计算π值
 * 数学原理：∫₀¹ 4/(1+x²) dx = π
 * 使用矩形法则进行数值积分
 * 
 * 编译: mpic++ -o pi_mpi pi_mpi.cpp -lm
 * 运行: mpirun -np 4 ./pi_mpi
 */

#include "mpi.h"
#include <stdio.h>
#include <math.h>

// 被积函数 f(x) = 4/(1+x²)
double f(double a) {
    return (4.0 / (1.0 + a * a));
}

int main(int argc, char *argv[]) {
    int done = 0, n, myid, numprocs, i;
    double PI25DT = 3.1415926535897932384626433;
    double mypi, pi, h, sum, x;
    double startwtime = 0.0, endwtime;
    int namelen;
    char processor_name[MPI_MAX_PROCESSOR_NAME];
    
    // 初始化MPI环境
    MPI_Init(&argc, &argv);
    
    // 获取进程总数
    MPI_Comm_size(MPI_COMM_WORLD, &numprocs);
    
    // 获取当前进程的rank
    MPI_Comm_rank(MPI_COMM_WORLD, &myid);
    
    // 获取处理器名称
    MPI_Get_processor_name(processor_name, &namelen);
    
    fprintf(stderr, "Process %d on %s\n", myid, processor_name);
    
    // 设置积分区间分割数量
    n = 10000000;  // 1000万个区间
    
    while (!done) {
        if (myid == 0) {
            // 主进程开始计时
            startwtime = MPI_Wtime();
            
            printf("计算π值 - MPI并行版本\n");
            printf("进程数量: %d\n", numprocs);
            printf("积分区间数量: %d\n", n);
            printf("理论π值: %.16f\n", PI25DT);
        }
        
        // 广播n值到所有进程
        MPI_Bcast(&n, 1, MPI_INT, 0, MPI_COMM_WORLD);
        
        if (n == 0) {
            done = 1;
        } else {
            h = 1.0 / (double)n;
            sum = 0.0;
            
            // 每个进程计算自己负责的部分
            // 进程myid负责计算从myid+1开始，步长为numprocs的所有点
            for (i = myid + 1; i <= n; i += numprocs) {
                x = h * ((double)i - 0.5);
                sum += f(x);
            }
            mypi = h * sum;
            
            // 使用MPI_Reduce收集所有进程的结果
            MPI_Reduce(&mypi, &pi, 1, MPI_DOUBLE, MPI_SUM, 0, MPI_COMM_WORLD);
            
            if (myid == 0) {
                // 主进程输出结果
                printf("计算π值: %.16f\n", pi);
                printf("误差: %.16f\n", fabs(pi - PI25DT));
                
                endwtime = MPI_Wtime();
                printf("计算时间: %.6f 秒\n", endwtime - startwtime);
                
                done = 1;  // 设置完成标志
            }
        }
    }
    
    // 结束MPI环境
    MPI_Finalize();
    
    return 0;
}
