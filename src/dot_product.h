#ifndef DOT_PRODUCT_H
#define DOT_PRODUCT_H

#include <vector>
#include <chrono>
#include <iostream>
#include <iomanip>
#include <random>
#include <fstream>

// 基础数据类型定义
using Vector = std::vector<double>;
using TimePoint = std::chrono::high_resolution_clock::time_point;

// 性能测试结果结构体
struct BenchmarkResult {
    double execution_time;
    double result_value;
    std::string method_name;
    int vector_size;
    int num_threads;
    
    BenchmarkResult(double time, double result, const std::string& method, 
                   int size, int threads = 1) 
        : execution_time(time), result_value(result), method_name(method),
          vector_size(size), num_threads(threads) {}
};

// 工具函数声明
Vector generate_random_vector(int size, double min_val = 0.0, double max_val = 1.0);
void print_vector_info(const Vector& vec, const std::string& name);
double measure_execution_time(std::function<double()> func);
void save_results_to_csv(const std::vector<BenchmarkResult>& results, 
                        const std::string& filename);

// 串行版本
double serial_dot_product(const Vector& a, const Vector& b);

// OpenMP版本
double openmp_dot_product(const Vector& a, const Vector& b, int num_threads = 4);

// MPI版本相关函数
#ifdef USE_MPI
#include <mpi.h>
double mpi_dot_product(const Vector& a, const Vector& b, int rank, int size);
void distribute_vector(const Vector& global_vec, Vector& local_vec, 
                      int rank, int size);
#endif

// 性能测试函数
void run_comprehensive_benchmark(const std::vector<int>& sizes,
                               const std::vector<int>& thread_counts);

#endif // DOT_PRODUCT_H
