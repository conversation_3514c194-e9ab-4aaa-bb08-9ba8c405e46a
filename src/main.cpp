#include "dot_product.h"
#include <iostream>
#include <vector>

/**
 * 点积并行计算主程序
 * 
 * 本程序实现了向量点积运算的三种并行计算方法：
 * 1. 串行计算 - 基础实现，作为性能对比基准
 * 2. OpenMP并行计算 - 共享内存并行，适用于多核处理器
 * 3. MPI分布式计算 - 分布式内存并行，适用于集群环境
 * 
 * 程序提供了全面的性能测试和分析功能，包括：
 * - 不同向量规模的性能测试
 * - 多种线程配置的可扩展性分析  
 * - 详细的性能指标统计和可视化
 */

void print_program_header() {
    std::cout << "========================================" << std::endl;
    std::cout << "    向量点积并行计算性能测试系统" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "支持的并行计算方法:" << std::endl;
    std::cout << "  • 串行计算 (Serial)" << std::endl;
    std::cout << "  • OpenMP共享内存并行" << std::endl;
    std::cout << "  • MPI分布式并行" << std::endl;
    std::cout << "========================================" << std::endl << std::endl;
}

void demonstrate_basic_functionality() {
    std::cout << "=== 基础功能演示 ===" << std::endl;
    
    // 创建小规模测试向量
    const int demo_size = 10;
    Vector a = generate_random_vector(demo_size, 1.0, 5.0);
    Vector b = generate_random_vector(demo_size, 1.0, 5.0);
    
    print_vector_info(a, "向量A");
    print_vector_info(b, "向量B");
    
    // 串行计算
    double serial_result = serial_dot_product(a, b);
    std::cout << "\n串行计算结果: " << std::fixed << std::setprecision(6) 
              << serial_result << std::endl;
    
    // OpenMP计算
    double openmp_result = openmp_dot_product(a, b, 4);
    std::cout << "OpenMP计算结果: " << std::fixed << std::setprecision(6) 
              << openmp_result << std::endl;
    
    // 验证结果一致性
    double error = std::abs(openmp_result - serial_result) / std::abs(serial_result);
    std::cout << "相对误差: " << std::scientific << error << std::endl;
    
    if (error < 1e-12) {
        std::cout << "✓ 计算结果验证通过" << std::endl;
    } else {
        std::cout << "✗ 计算结果存在误差" << std::endl;
    }
    
    std::cout << std::endl;
}

void run_scalability_tests() {
    std::cout << "=== 可扩展性测试 ===" << std::endl;
    
    // 测试不同规模的向量
    std::vector<int> test_sizes = {1000, 10000, 100000, 1000000};
    std::vector<int> thread_counts = {1, 2, 4, 8};
    
    for (int size : test_sizes) {
        std::cout << "\n向量规模: " << size << std::endl;
        std::cout << std::string(40, '-') << std::endl;
        
        Vector a = generate_random_vector(size);
        Vector b = generate_random_vector(size);
        
        // 测试OpenMP可扩展性
        test_openmp_scalability(a, b);
        
        // 分析负载均衡
        analyze_openmp_load_balance(a, b, 4);
    }
}

void interactive_mode() {
    std::cout << "=== 交互式测试模式 ===" << std::endl;
    
    int vector_size, num_threads;
    
    std::cout << "请输入向量大小: ";
    std::cin >> vector_size;
    
    std::cout << "请输入线程数量: ";
    std::cin >> num_threads;
    
    if (vector_size <= 0 || num_threads <= 0) {
        std::cout << "输入参数无效！" << std::endl;
        return;
    }
    
    std::cout << "\n生成测试向量..." << std::endl;
    Vector a = generate_random_vector(vector_size);
    Vector b = generate_random_vector(vector_size);
    
    std::cout << "开始性能测试..." << std::endl;
    
    // 串行测试
    auto start = std::chrono::high_resolution_clock::now();
    double serial_result = serial_dot_product(a, b);
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    double serial_time = duration.count() / 1000.0;
    
    // OpenMP测试
    start = std::chrono::high_resolution_clock::now();
    double openmp_result = openmp_dot_product(a, b, num_threads);
    end = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    double openmp_time = duration.count() / 1000.0;
    
    // 结果输出
    std::cout << "\n=== 测试结果 ===" << std::endl;
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "串行时间: " << serial_time << " ms" << std::endl;
    std::cout << "OpenMP时间: " << openmp_time << " ms" << std::endl;
    std::cout << "加速比: " << serial_time / openmp_time << std::endl;
    std::cout << "效率: " << (serial_time / openmp_time) / num_threads * 100 << "%" << std::endl;
    std::cout << std::scientific << std::setprecision(10);
    std::cout << "计算结果: " << serial_result << std::endl;
}

int main() {
    print_program_header();
    
    // 创建结果目录
    system("mkdir -p results");
    
    int choice;
    
    do {
        std::cout << "请选择测试模式:" << std::endl;
        std::cout << "1. 基础功能演示" << std::endl;
        std::cout << "2. 可扩展性测试" << std::endl;
        std::cout << "3. 综合性能测试" << std::endl;
        std::cout << "4. 交互式测试" << std::endl;
        std::cout << "0. 退出程序" << std::endl;
        std::cout << "请输入选择 (0-4): ";
        
        std::cin >> choice;
        
        switch (choice) {
            case 1:
                demonstrate_basic_functionality();
                break;
                
            case 2:
                run_scalability_tests();
                break;
                
            case 3: {
                std::vector<int> sizes = {10000, 100000, 1000000};
                std::vector<int> threads = {1, 2, 4, 8};
                run_comprehensive_benchmark(sizes, threads);
                break;
            }
            
            case 4:
                interactive_mode();
                break;
                
            case 0:
                std::cout << "程序退出。" << std::endl;
                break;
                
            default:
                std::cout << "无效选择，请重新输入。" << std::endl;
                break;
        }
        
        if (choice != 0) {
            std::cout << "\n按回车键继续...";
            std::cin.ignore();
            std::cin.get();
            std::cout << std::endl;
        }
        
    } while (choice != 0);
    
    return 0;
}
