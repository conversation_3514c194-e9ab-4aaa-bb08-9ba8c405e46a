/*
 * 串行版本：使用数值积分计算π值
 * 数学原理：∫₀¹ 4/(1+x²) dx = π
 * 使用矩形法则进行数值积分
 * 
 * 编译: g++ -o pi_serial pi_serial.cpp -lm
 * 运行: ./pi_serial
 */

#include <stdio.h>
#include <math.h>
#include <time.h>

// 被积函数 f(x) = 4/(1+x²)
double f(double x) {
    return 4.0 / (1.0 + x * x);
}

// 串行计算π值
double calculate_pi_serial(int n) {
    double h = 1.0 / (double)n;  // 步长
    double sum = 0.0;
    
    // 使用矩形法则计算积分
    for (int i = 1; i <= n; i++) {
        double x = h * ((double)i - 0.5);  // 矩形中点
        sum += f(x);
    }
    
    return h * sum;
}

int main() {
    // 设置积分区间分割数量
    int n = 10000000;  // 1000万个区间
    
    // 精确的π值（25位小数）
    double PI25DT = 3.1415926535897932384626433;
    
    printf("计算π值 - 串行版本\n");
    printf("积分区间数量: %d\n", n);
    printf("理论π值: %.16f\n", PI25DT);
    
    // 开始计时
    clock_t start_time = clock();
    
    // 计算π值
    double pi_calculated = calculate_pi_serial(n);
    
    // 结束计时
    clock_t end_time = clock();
    
    // 计算误差
    double error = fabs(pi_calculated - PI25DT);
    
    // 计算运行时间
    double cpu_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;
    
    // 输出结果
    printf("计算π值: %.16f\n", pi_calculated);
    printf("误差: %.16f\n", error);
    printf("计算时间: %.6f 秒\n", cpu_time);
    
    return 0;
}
