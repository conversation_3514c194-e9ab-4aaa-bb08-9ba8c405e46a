#include "dot_product.h"
#include <algorithm>
#include <sstream>
#include <map>

// 前向声明
void analyze_performance_trends(const std::vector<BenchmarkResult>& results);

/**
 * 保存测试结果到CSV文件
 */
void save_results_to_csv(const std::vector<BenchmarkResult>& results, 
                        const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filename << std::endl;
        return;
    }
    
    // 写入CSV头部
    file << "Method,VectorSize,NumThreads,ExecutionTime(ms),Result,Speedup,Efficiency(%)\n";
    
    // 计算基准时间（串行版本）
    std::map<int, double> serial_times;
    for (const auto& result : results) {
        if (result.method_name == "Serial") {
            serial_times[result.vector_size] = result.execution_time;
        }
    }
    
    // 写入数据
    for (const auto& result : results) {
        double speedup = 1.0;
        double efficiency = 100.0;
        
        if (serial_times.count(result.vector_size) > 0 && result.method_name != "Serial") {
            speedup = serial_times[result.vector_size] / result.execution_time;
            efficiency = speedup / result.num_threads * 100.0;
        }
        
        file << result.method_name << ","
             << result.vector_size << ","
             << result.num_threads << ","
             << std::fixed << std::setprecision(6) << result.execution_time << ","
             << std::scientific << std::setprecision(10) << result.result_value << ","
             << std::fixed << std::setprecision(3) << speedup << ","
             << std::setprecision(2) << efficiency << "\n";
    }
    
    file.close();
    std::cout << "结果已保存到: " << filename << std::endl;
}

/**
 * 综合性能测试函数
 */
void run_comprehensive_benchmark(const std::vector<int>& sizes,
                               const std::vector<int>& thread_counts) {
    std::vector<BenchmarkResult> results;
    
    std::cout << "\n=== 点积并行计算综合性能测试 ===" << std::endl;
    std::cout << "测试配置:" << std::endl;
    std::cout << "  向量大小: ";
    for (int size : sizes) std::cout << size << " ";
    std::cout << std::endl;
    std::cout << "  线程数量: ";
    for (int threads : thread_counts) std::cout << threads << " ";
    std::cout << std::endl << std::endl;
    
    for (int size : sizes) {
        std::cout << "测试向量大小: " << size << std::endl;
        std::cout << std::string(50, '-') << std::endl;
        
        // 生成测试向量
        Vector a = generate_random_vector(size, 0.0, 1.0);
        Vector b = generate_random_vector(size, 0.0, 1.0);
        
        // 串行版本测试
        auto start = std::chrono::high_resolution_clock::now();
        double serial_result = serial_dot_product(a, b);
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        double serial_time = duration.count() / 1000.0;
        
        results.emplace_back(serial_time, serial_result, "Serial", size, 1);
        
        std::cout << "串行版本: " << std::fixed << std::setprecision(3) 
                  << serial_time << " ms" << std::endl;
        
        // OpenMP版本测试
        for (int threads : thread_counts) {
            if (threads == 1) continue; // 跳过单线程，已在串行版本测试
            
            start = std::chrono::high_resolution_clock::now();
            double openmp_result = openmp_dot_product(a, b, threads);
            end = std::chrono::high_resolution_clock::now();
            duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            double openmp_time = duration.count() / 1000.0;
            
            double speedup = serial_time / openmp_time;
            double efficiency = speedup / threads * 100.0;
            
            results.emplace_back(openmp_time, openmp_result, "OpenMP", size, threads);
            
            std::cout << "OpenMP(" << threads << "线程): " 
                      << std::setprecision(3) << openmp_time << " ms"
                      << " | 加速比: " << speedup 
                      << " | 效率: " << efficiency << "%" << std::endl;
            
            // 验证结果正确性
            double error = std::abs(openmp_result - serial_result) / std::abs(serial_result);
            if (error > 1e-10) {
                std::cout << "  警告: 结果误差较大 (" << error << ")" << std::endl;
            }
        }
        
        std::cout << std::endl;
    }
    
    // 保存结果
    save_results_to_csv(results, "results/benchmark_results.csv");
    
    // 性能分析总结
    std::cout << "\n=== 性能分析总结 ===" << std::endl;
    analyze_performance_trends(results);
}

/**
 * 性能趋势分析
 */
void analyze_performance_trends(const std::vector<BenchmarkResult>& results) {
    std::map<std::string, std::vector<double>> method_speedups;
    std::map<int, double> serial_times;
    
    // 收集串行基准时间
    for (const auto& result : results) {
        if (result.method_name == "Serial") {
            serial_times[result.vector_size] = result.execution_time;
        }
    }
    
    // 计算各方法的加速比
    for (const auto& result : results) {
        if (result.method_name != "Serial" && serial_times.count(result.vector_size) > 0) {
            double speedup = serial_times[result.vector_size] / result.execution_time;
            method_speedups[result.method_name + "_" + std::to_string(result.num_threads)].push_back(speedup);
        }
    }
    
    // 输出平均性能表现
    std::cout << "平均加速比表现:" << std::endl;
    for (const auto& pair : method_speedups) {
        double avg_speedup = 0.0;
        for (double speedup : pair.second) {
            avg_speedup += speedup;
        }
        avg_speedup /= pair.second.size();
        
        std::cout << "  " << pair.first << ": " 
                  << std::fixed << std::setprecision(2) << avg_speedup << "x" << std::endl;
    }
}
