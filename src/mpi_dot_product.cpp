#include "dot_product.h"
#include <mpi.h>

/**
 * MPI并行点积计算实现
 * 
 * 该函数采用MPI分布式并行计算模式实现向量点积运算。通过将向量
 * 数据分块分发到不同进程，各进程独立计算局部点积，最后通过
 * MPI_Reduce操作汇总全局结果。适用于大规模分布式计算环境。
 * 
 * @param a 第一个输入向量
 * @param b 第二个输入向量
 * @param rank 当前进程编号
 * @param size 总进程数量
 * @return 两向量的点积结果
 */
double mpi_dot_product(const Vector& a, const Vector& b, int rank, int size) {
    if (a.size() != b.size()) {
        throw std::invalid_argument("向量维度不匹配");
    }
    
    const int n = a.size();
    const int local_n = n / size;
    const int remainder = n % size;
    
    // 计算每个进程的数据分布
    int local_size = local_n + (rank < remainder ? 1 : 0);
    int start_idx = rank * local_n + std::min(rank, remainder);
    
    // 计算局部点积
    double local_sum = 0.0;
    for (int i = 0; i < local_size; ++i) {
        int global_idx = start_idx + i;
        if (global_idx < n) {
            local_sum += a[global_idx] * b[global_idx];
        }
    }
    
    // 使用MPI_Reduce汇总全局结果
    double global_sum = 0.0;
    MPI_Reduce(&local_sum, &global_sum, 1, MPI_DOUBLE, MPI_SUM, 0, MPI_COMM_WORLD);
    
    return global_sum;
}

/**
 * 向量数据分发函数
 * 
 * 将全局向量数据分发到各个MPI进程的本地存储中
 */
void distribute_vector(const Vector& global_vec, Vector& local_vec, 
                      int rank, int size) {
    const int n = global_vec.size();
    const int local_n = n / size;
    const int remainder = n % size;
    
    // 计算本地向量大小
    int local_size = local_n + (rank < remainder ? 1 : 0);
    local_vec.resize(local_size);
    
    // 计算数据分发的偏移量和大小
    std::vector<int> sendcounts(size);
    std::vector<int> displs(size);
    
    for (int i = 0; i < size; ++i) {
        sendcounts[i] = local_n + (i < remainder ? 1 : 0);
        displs[i] = i * local_n + std::min(i, remainder);
    }
    
    // 使用MPI_Scatterv分发数据
    MPI_Scatterv(global_vec.data(), sendcounts.data(), displs.data(), MPI_DOUBLE,
                 local_vec.data(), local_size, MPI_DOUBLE, 0, MPI_COMM_WORLD);
}

/**
 * MPI性能分析函数
 */
void analyze_mpi_performance(const Vector& a, const Vector& b, int rank, int size) {
    if (rank == 0) {
        std::cout << "\n=== MPI性能分析 ===" << std::endl;
        std::cout << "总进程数: " << size << std::endl;
        std::cout << "向量大小: " << a.size() << std::endl;
    }
    
    // 测量通信开销
    MPI_Barrier(MPI_COMM_WORLD);
    auto comm_start = std::chrono::high_resolution_clock::now();
    
    // 模拟数据分发
    Vector local_a, local_b;
    distribute_vector(a, local_a, rank, size);
    distribute_vector(b, local_b, rank, size);
    
    MPI_Barrier(MPI_COMM_WORLD);
    auto comm_end = std::chrono::high_resolution_clock::now();
    
    // 测量计算时间
    auto comp_start = std::chrono::high_resolution_clock::now();
    double result = mpi_dot_product(a, b, rank, size);
    auto comp_end = std::chrono::high_resolution_clock::now();
    
    auto comm_duration = std::chrono::duration_cast<std::chrono::microseconds>(comm_end - comm_start);
    auto comp_duration = std::chrono::duration_cast<std::chrono::microseconds>(comp_end - comp_start);
    
    double comm_time = comm_duration.count() / 1000.0;
    double comp_time = comp_duration.count() / 1000.0;
    
    if (rank == 0) {
        std::cout << std::fixed << std::setprecision(3);
        std::cout << "通信时间: " << comm_time << " ms" << std::endl;
        std::cout << "计算时间: " << comp_time << " ms" << std::endl;
        std::cout << "总时间: " << comm_time + comp_time << " ms" << std::endl;
        std::cout << "通信/计算比: " << comm_time / comp_time << std::endl;
        std::cout << "计算结果: " << result << std::endl;
    }
}
