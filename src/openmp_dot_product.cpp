#include "dot_product.h"
#include <omp.h>

/**
 * OpenMP并行点积计算实现
 * 
 * 该函数利用OpenMP并行化技术实现向量点积运算。通过将计算任务
 * 分配给多个线程，显著提升大规模向量计算的性能。采用reduction
 * 操作确保线程安全的累加操作。
 * 
 * @param a 第一个输入向量
 * @param b 第二个输入向量  
 * @param num_threads 使用的线程数量
 * @return 两向量的点积结果
 */
double openmp_dot_product(const Vector& a, const Vector& b, int num_threads) {
    if (a.size() != b.size()) {
        throw std::invalid_argument("向量维度不匹配");
    }
    
    double sum = 0.0;
    const size_t n = a.size();
    
    // 设置OpenMP线程数
    omp_set_num_threads(num_threads);
    
    // 使用OpenMP并行化for循环，采用reduction操作进行安全累加
    #pragma omp parallel for reduction(+:sum)
    for (size_t i = 0; i < n; ++i) {
        sum += a[i] * b[i];
    }
    
    return sum;
}

/**
 * OpenMP性能测试函数
 * 
 * 测试不同线程数下的OpenMP性能表现，分析并行化效率
 * 和加速比特性。
 */
void test_openmp_scalability(const Vector& a, const Vector& b) {
    std::cout << "\n=== OpenMP可扩展性测试 ===" << std::endl;
    std::cout << "向量大小: " << a.size() << std::endl;
    
    // 测试不同线程数的性能
    std::vector<int> thread_counts = {1, 2, 4, 8, 16};
    double serial_time = 0.0;
    
    for (int threads : thread_counts) {
        auto start = std::chrono::high_resolution_clock::now();
        double result = openmp_dot_product(a, b, threads);
        auto end = std::chrono::high_resolution_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        double time_ms = duration.count() / 1000.0;
        
        if (threads == 1) {
            serial_time = time_ms;
        }
        
        double speedup = (threads == 1) ? 1.0 : serial_time / time_ms;
        double efficiency = speedup / threads * 100.0;
        
        std::cout << std::fixed << std::setprecision(3);
        std::cout << "线程数: " << std::setw(2) << threads 
                  << " | 时间: " << std::setw(8) << time_ms << " ms"
                  << " | 加速比: " << std::setw(6) << speedup
                  << " | 效率: " << std::setw(6) << efficiency << "%"
                  << " | 结果: " << std::setw(12) << result << std::endl;
    }
}

/**
 * OpenMP线程负载均衡分析
 */
void analyze_openmp_load_balance(const Vector& a, const Vector& b, int num_threads) {
    std::cout << "\n=== OpenMP负载均衡分析 ===" << std::endl;
    
    omp_set_num_threads(num_threads);
    std::vector<double> thread_times(num_threads, 0.0);
    std::vector<int> thread_iterations(num_threads, 0);
    
    double sum = 0.0;
    const size_t n = a.size();
    
    #pragma omp parallel reduction(+:sum)
    {
        int thread_id = omp_get_thread_num();
        auto thread_start = std::chrono::high_resolution_clock::now();
        
        #pragma omp for
        for (size_t i = 0; i < n; ++i) {
            sum += a[i] * b[i];
            thread_iterations[thread_id]++;
        }
        
        auto thread_end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(thread_end - thread_start);
        thread_times[thread_id] = duration.count() / 1000.0;
    }
    
    std::cout << "线程负载分布:" << std::endl;
    for (int i = 0; i < num_threads; ++i) {
        std::cout << "线程 " << i << ": " 
                  << thread_iterations[i] << " 次迭代, "
                  << std::fixed << std::setprecision(3) 
                  << thread_times[i] << " ms" << std::endl;
    }
}
