# Makefile for π calculation programs
# 编译所有版本的π计算程序

CC = gcc
CXX = g++
MPICC = mpic++
CFLAGS = -O2 -lm
CXXFLAGS = -O2 -lm
OMPFLAGS = -fopenmp

# 默认目标：编译所有程序
all: pi_serial pi_mpi pi_openmp

# 串行版本
pi_serial: pi_serial.cpp
	$(CXX) $(CXXFLAGS) -o pi_serial pi_serial.cpp

# MPI并行版本
pi_mpi: pi_mpi.cpp
	$(MPICC) $(CXXFLAGS) -o pi_mpi pi_mpi.cpp

# OpenMP并行版本
pi_openmp: pi_openmp.cpp
	$(CXX) $(CXXFLAGS) $(OMPFLAGS) -o pi_openmp pi_openmp.cpp

# 清理编译生成的文件
clean:
	rm -f pi_serial pi_mpi pi_openmp

# 运行测试
test: all
	@echo "=== 运行串行版本 ==="
	./pi_serial
	@echo ""
	@echo "=== 运行OpenMP版本 ==="
	./pi_openmp
	@echo ""
	@echo "=== 运行MPI版本（需要MPI环境）==="
	@if command -v mpirun >/dev/null 2>&1; then \
		mpirun -np 4 ./pi_mpi; \
	else \
		echo "MPI环境未安装，跳过MPI测试"; \
	fi

# 运行Python版本
test-python:
	@echo "=== 运行Python串行版本 ==="
	python3 pi_serial.py
	@echo ""
	@echo "=== 运行Python多线程版本 ==="
	python3 pi_openmp.py
	@echo ""
	@echo "=== 运行Python MPI版本 ==="
	@if command -v mpirun >/dev/null 2>&1 && python3 -c "import mpi4py" 2>/dev/null; then \
		mpirun -np 4 python3 pi_mpi.py; \
	else \
		echo "MPI环境或mpi4py未安装，运行模拟模式"; \
		python3 pi_mpi.py; \
	fi

# 安装Python依赖
install-python-deps:
	pip3 install mpi4py

.PHONY: all clean test test-python install-python-deps
