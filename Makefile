# Makefile for π calculation parallel computing research
# 高性能π值计算并行程序构建脚本

CXX = g++
MPICC = mpic++
CXXFLAGS = -O3 -Wall -std=c++11
OMPFLAGS = -fopenmp
SRCDIR = src
SCRIPTDIR = scripts
RESULTSDIR = results

# 默认目标：编译所有程序
all: pi_serial pi_mpi pi_openmp benchmark

# 串行版本
pi_serial: $(SRCDIR)/pi_serial.cpp
	$(CXX) $(CXXFLAGS) -o pi_serial $(SRCDIR)/pi_serial.cpp -lm

# MPI并行版本
pi_mpi: $(SRCDIR)/pi_mpi.cpp
	$(MPICC) $(CXXFLAGS) -o pi_mpi $(SRCDIR)/pi_mpi.cpp -lm

# OpenMP并行版本
pi_openmp: $(SRCDIR)/pi_openmp.cpp
	$(CXX) $(CXXFLAGS) $(OMPFLAGS) -o pi_openmp $(SRCDIR)/pi_openmp.cpp -lm

# 性能测试程序
benchmark: $(SCRIPTDIR)/benchmark.cpp
	$(CXX) $(CXXFLAGS) $(OMPFLAGS) -o benchmark $(SCRIPTDIR)/benchmark.cpp -lm

# 创建结果目录
$(RESULTSDIR):
	mkdir -p $(RESULTSDIR)

# 运行完整实验
experiment: all $(RESULTSDIR)
	@echo "开始完整性能实验..."
	./benchmark
	cd $(SCRIPTDIR) && python3 visualize.py
	@echo "实验完成，结果保存在 $(RESULTSDIR)/ 目录"

# 快速测试
test: all
	@echo "=== 串行版本测试 ==="
	./pi_serial
	@echo ""
	@echo "=== OpenMP版本测试 ==="
	./pi_openmp
	@echo ""
	@echo "=== MPI版本测试 ==="
	@if command -v mpirun >/dev/null 2>&1; then \
		mpirun -np 4 ./pi_mpi; \
	else \
		echo "MPI环境未安装，跳过MPI测试"; \
	fi

# 清理编译文件
clean:
	rm -f pi_serial pi_mpi pi_openmp benchmark

# 清理所有生成文件
clean-all: clean
	rm -rf $(RESULTSDIR)

# 安装Python依赖
install-deps:
	pip3 install pandas matplotlib seaborn numpy

.PHONY: all test experiment clean clean-all install-deps
