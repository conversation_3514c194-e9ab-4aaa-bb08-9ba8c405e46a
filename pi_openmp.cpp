/*
 * OpenMP并行版本：使用数值积分计算π值
 * 数学原理：∫₀¹ 4/(1+x²) dx = π
 * 使用矩形法则进行数值积分
 * 
 * 编译: g++ -fopenmp -o pi_openmp pi_openmp.cpp -lm
 * 运行: ./pi_openmp
 * 或者设置线程数: export OMP_NUM_THREADS=4 && ./pi_openmp
 */

#include <stdio.h>
#include <stdlib.h>
#include <omp.h>
#include <math.h>

#define NUM_SAMPLES 10000000  // 定义积分区间数量

// 被积函数 f(x) = 4/(1+x²)
double f(double a) {
    return (4.0 / (1.0 + a * a));
}

int main() {
    int i;
    double x, sum = 0.0, pi;
    double h = 1.0 / (double)NUM_SAMPLES;
    
    printf("计算π值 - OpenMP并行版本\n");
    printf("积分区间数量: %d\n", NUM_SAMPLES);
    printf("理论π值: %.16f\n", M_PI);
    
    // 获取线程数
    int num_threads;
    #pragma omp parallel
    {
        #pragma omp single
        num_threads = omp_get_num_threads();
    }
    printf("线程数量: %d\n", num_threads);
    
    // 开始计时
    double start_time = omp_get_wtime();
    
    // OpenMP并行计算
    #pragma omp parallel for private(x) reduction(+:sum)
    for (i = 0; i < NUM_SAMPLES; i++) {
        x = h * ((double)i + 0.5);
        sum += f(x);
    }

    pi = h * sum;
    
    // 结束计时
    double end_time = omp_get_wtime();
    
    // 输出结果
    printf("计算π值: %.16f\n", pi);
    printf("误差: %.16f\n", fabs(pi - M_PI));
    printf("计算时间: %.6f 秒\n", end_time - start_time);
    
    return 0;
}
