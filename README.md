# π值计算程序集合

本项目实现了使用数值积分方法计算π值的多种版本，包括串行、MPI并行和OpenMP并行版本，同时提供了Python和C++两种语言的实现。

## 数学原理

使用数值积分计算π值：
```
∫₀¹ 4/(1+x²) dx = π
```

使用矩形法则进行数值积分：
```
π ≈ (1/n) × Σ f(xi)，其中 xi = (i-0.5)/n
```

## 文件结构

### Python版本
- `pi_serial.py` - 串行版本
- `pi_mpi.py` - MPI并行版本
- `pi_openmp.py` - 多线程版本（模拟OpenMP）

### C++版本
- `pi_serial.cpp` - 串行版本
- `pi_mpi.cpp` - MPI并行版本
- `pi_openmp.cpp` - OpenMP并行版本

### 其他文件
- `Makefile` - 编译脚本
- `README.md` - 说明文档

## 编译和运行

### C++版本

#### 编译所有程序
```bash
make all
```

#### 单独编译
```bash
# 串行版本
g++ -O2 -o pi_serial pi_serial.cpp -lm

# MPI版本（需要MPI环境）
mpic++ -O2 -o pi_mpi pi_mpi.cpp -lm

# OpenMP版本
g++ -O2 -fopenmp -o pi_openmp pi_openmp.cpp -lm
```

#### 运行程序
```bash
# 串行版本
./pi_serial

# OpenMP版本
./pi_openmp
# 或指定线程数
export OMP_NUM_THREADS=4
./pi_openmp

# MPI版本（需要MPI环境）
mpirun -np 4 ./pi_mpi
```

#### 运行所有测试
```bash
make test
```

### Python版本

#### 安装依赖
```bash
# 安装MPI支持（可选）
pip3 install mpi4py
# 或使用make命令
make install-python-deps
```

#### 运行程序
```bash
# 串行版本
python3 pi_serial.py

# 多线程版本
python3 pi_openmp.py

# MPI版本
mpirun -np 4 python3 pi_mpi.py
# 如果没有MPI环境，会自动使用模拟模式
python3 pi_mpi.py
```

#### 运行所有Python测试
```bash
make test-python
```

## 性能比较

程序会输出以下信息：
- 计算得到的π值
- 与理论值的误差
- 计算时间
- 使用的进程/线程数量

## 环境要求

### C++版本
- GCC编译器（支持C++11）
- OpenMP支持（通常GCC自带）
- MPI环境（如OpenMPI或MPICH，用于MPI版本）

### Python版本
- Python 3.6+
- mpi4py（用于MPI版本，可选）

## 安装MPI环境

### Ubuntu/Debian
```bash
sudo apt-get install openmpi-bin openmpi-common libopenmpi-dev
```

### CentOS/RHEL
```bash
sudo yum install openmpi openmpi-devel
# 或使用dnf
sudo dnf install openmpi openmpi-devel
```

### macOS
```bash
brew install open-mpi
```

## 算法说明

1. **串行版本**：单线程顺序计算所有积分区间
2. **MPI版本**：多进程并行，每个进程计算部分区间，最后汇总结果
3. **OpenMP版本**：多线程并行，使用reduction操作汇总结果

## 预期结果

使用1000万个积分区间，理论π值为3.1415926535897932...

各版本应该得到相似的结果，误差在10^-7量级，并行版本应该比串行版本有明显的性能提升。

## 清理编译文件

```bash
make clean
```
