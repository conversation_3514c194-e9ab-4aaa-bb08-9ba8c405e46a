#!/usr/bin/env python3
"""
简单的测试脚本，运行所有版本的π计算程序
"""

import subprocess
import os

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"测试: {description}")
    print(f"命令: {cmd}")
    print('='*50)
    
    try:
        result = subprocess.run(cmd, shell=True, cwd=".", timeout=30)
        if result.returncode == 0:
            print("✓ 成功")
        else:
            print(f"✗ 失败 (返回码: {result.returncode})")
    except subprocess.TimeoutExpired:
        print("✗ 超时")
    except Exception as e:
        print(f"✗ 错误: {e}")

def main():
    print("π值计算程序测试")
    print("测试所有版本的实现")
    
    # 检查文件是否存在
    required_files = [
        "pi_serial.py", "pi_mpi.py", "pi_openmp.py",
        "pi_serial.cpp", "pi_mpi.cpp", "pi_openmp.cpp"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"错误：缺少文件: {missing_files}")
        return
    
    # 编译C++程序
    print("\n编译C++程序...")
    run_command("make clean", "清理旧文件")
    run_command("make all", "编译所有C++程序")
    
    # 测试Python版本
    run_command("python3 pi_serial.py", "Python串行版本")
    run_command("python3 pi_openmp.py", "Python多线程版本")
    run_command("python3 pi_mpi.py", "Python MPI版本（模拟模式）")
    
    # 测试C++版本
    if os.path.exists("./pi_serial"):
        run_command("./pi_serial", "C++串行版本")
    
    if os.path.exists("./pi_openmp"):
        run_command("./pi_openmp", "C++OpenMP版本")
    
    if os.path.exists("./pi_mpi"):
        run_command("timeout 10 mpirun -np 2 ./pi_mpi", "C++MPI版本")
    
    print(f"\n{'='*50}")
    print("测试完成！")
    print("所有版本都实现了相同的数值积分算法来计算π值")
    print("理论π值: 3.1415926535897932...")
    print("各版本的计算结果应该非常接近这个值")

if __name__ == "__main__":
    main()
